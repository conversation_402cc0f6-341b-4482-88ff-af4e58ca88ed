package com.whatsapp.cms.common.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.util.SaResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(CustomException.class)
    public SaResult handleRenException(CustomException ex) {
        SaResult result = new SaResult();
        result.setCode(ex.getCode());
        result.setMsg(ex.getMsg());
        return result;
    }

    @ExceptionHandler(NotLoginException.class)
    public SaResult handleNotLoginException(NotLoginException ex) {
        logger.error("登录错误：{}", ex.getMessage());
        SaResult result = new SaResult();
        result.setCode(401);
        result.setMsg(ex.getMessage());
        return result;
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public SaResult handleDuplicateKeyException(DuplicateKeyException ex) {

        return SaResult.error("数据库数据重复");
    }

    @ExceptionHandler(Exception.class)
    public SaResult handleException(Exception ex) {
        logger.error(ex.getMessage(), ex);
        return SaResult.error("服务器错误，请联系管理员");
    }
}