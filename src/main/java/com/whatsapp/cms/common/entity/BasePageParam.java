package com.whatsapp.cms.common.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BasePageParam {

    private Long current;

    private Long pageSize;

    public <T> IPage<T> getPage() {
        if (current == null) {
            current = 1L;
        }
        if (pageSize == null) {
            pageSize = 20L;
        }
        return new Page<>(current, pageSize);
    }
}
