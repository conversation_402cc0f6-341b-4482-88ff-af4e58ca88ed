package com.whatsapp.cms.common.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class BasePageResult<T> {

    private List<T> list;

    private int total;

    private int current;

    public BasePageResult(List<T> list, int total, int current) {
        this.list = list;
        this.total = total;
        this.current = current;
    }

    public static <E> BasePageResult<E> newInstance(IPage<E> page) {
        return new BasePageResult<>(page.getRecords(), (int) page.getTotal(), (int) page.getCurrent());
    }
}
