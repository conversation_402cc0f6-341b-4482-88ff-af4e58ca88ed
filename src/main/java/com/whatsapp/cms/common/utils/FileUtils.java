package com.whatsapp.cms.common.utils;

import com.whatsapp.cms.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

@Slf4j
public class FileUtils {
    public static File multipartFileToFile(MultipartFile multiFile) {
        // 获取文件名
        String fileName = multiFile.getOriginalFilename();
        // 获取文件后缀(.xml)
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        // 若要防止生成的临时文件重复,需要在文件名后添加随机码
        try {
            //"tmp", ".txt"
            // fileName这块可以根据自己的业务需求进行修改，我这里没有做任何处理
            File file = File.createTempFile(fileName, suffix);
            multiFile.transferTo(file);
            return file;
        } catch (Exception e) {
            log.error("MultipartFile转File失败,{}", e.getMessage());
            throw new CustomException("上传文件失败");
        }
    }
}
