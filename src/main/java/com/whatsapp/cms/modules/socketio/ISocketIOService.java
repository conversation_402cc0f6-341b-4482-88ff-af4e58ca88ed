package com.whatsapp.cms.modules.socketio;

import java.util.Set;

public interface ISocketIOService {

    /**
     * 启动服务
     */
    void start();

    /**
     * 停止服务
     */
    void stop();

    /**
     * 推送信息给指定客户端
     *
     * @param userId:     客户端唯一标识
     * @param msgContent: 消息内容
     */
    Boolean pushMessageToUser(String userId, String msgContent);

    /**
     * 发送广播消息
     *
     * @param broad
     * @param content
     */
    void pushBroadMessage(String broad, String content);

    Set<String> getAdmin();
}
