package com.whatsapp.cms.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.whatsapp.cms.modules.entity.AccountGroup;
import com.whatsapp.cms.modules.form.AccountGroupForm;

/**
 * <p>
 * 账号分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
public interface IAccountGroupService extends IService<AccountGroup> {

    void saveAccountGroup(AccountGroupForm accountGroup);

    void update(AccountGroupForm tbAccountGroup);

    void delete(Long id);

    String getAccountGroupName(Long id);
}
