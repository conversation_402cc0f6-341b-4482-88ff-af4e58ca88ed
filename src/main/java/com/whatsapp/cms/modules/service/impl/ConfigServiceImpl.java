package com.whatsapp.cms.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.whatsapp.cms.modules.entity.Config;
import com.whatsapp.cms.modules.mapper.ConfigMapper;
import com.whatsapp.cms.modules.service.IConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Service
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, Config> implements IConfigService {

    @Override
    @Transactional
    public void changeProxy(Long id) {
        this.update(new LambdaUpdateWrapper<Config>().eq(Config::getId, id).set(Config::getIsUse, 1));
        this.update(new LambdaUpdateWrapper<Config>().ne(Config::getId, id).set(Config::getIsUse, 0));
    }
}
