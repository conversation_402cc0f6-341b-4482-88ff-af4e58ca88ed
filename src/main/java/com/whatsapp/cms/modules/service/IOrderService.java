package com.whatsapp.cms.modules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.whatsapp.cms.modules.dto.OrderDto;
import com.whatsapp.cms.modules.entity.Order;
import com.whatsapp.cms.modules.form.AddAdminForm;
import com.whatsapp.cms.modules.form.OrderForm;
import com.whatsapp.cms.modules.form.UpdateOrderForm;
import com.whatsapp.cms.modules.query.OrderQueryParam;

/**
 * <p>
 * 任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
public interface IOrderService extends IService<Order> {

    IPage<Order> queryPage(OrderQueryParam param);
    /**
     * 创建任务
     * @param form
     */
    void createOrder(OrderForm form);

    void startOrder(Long id);

    /**
     * 获取任务详情
     * @param id
     * @return
     */
    OrderDto orderInfo(Long id);

    /**
     * 更新任务
     * @param form
     */
    void updateOrder(UpdateOrderForm form);

    void delOrder(Long id);

    void autoAddAdmin(AddAdminForm form);
}
