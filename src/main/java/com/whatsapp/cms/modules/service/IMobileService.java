package com.whatsapp.cms.modules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.whatsapp.cms.modules.dto.GroupDto;
import com.whatsapp.cms.modules.entity.Mobile;
import com.whatsapp.cms.modules.query.MobileQueryParam;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 料子 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
public interface IMobileService extends IService<Mobile> {

    IPage<Mobile> queryPage(MobileQueryParam param);

    List<String> filterMobile(GroupDto groupDto);

    Long countMobileByGroupId(Long groupId);

    Map<Long,Long> countMobileByGroupIds(List<Long> groupIds);
}
