package com.whatsapp.cms.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.whatsapp.cms.modules.dto.AccountOneDto;
import com.whatsapp.cms.modules.dto.LaDto;
import com.whatsapp.cms.modules.entity.AccountInGroup;

/**
 * <p>
 * 群组账号关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
public interface IAccountInGroupService extends IService<AccountInGroup> {

    AccountOneDto getLaByGroupId(Long groupId);
}
