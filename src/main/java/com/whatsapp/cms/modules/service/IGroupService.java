package com.whatsapp.cms.modules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.whatsapp.cms.modules.dto.GroupInfoDto;
import com.whatsapp.cms.modules.dto.LaDto;
import com.whatsapp.cms.modules.entity.Account;
import com.whatsapp.cms.modules.entity.Group;
import com.whatsapp.cms.modules.form.*;
import com.whatsapp.cms.modules.query.GroupQueryParam;

import java.util.List;

/**
 * <p>
 * 群组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
public interface IGroupService extends IService<Group> {

    IPage<Group> queryPage(GroupQueryParam param);
    /**
     * 开启群组订单
     * @param id
     */
    void startGroupOder(Long id);

    /**
     * 更新群组信息
     * @param form
     */
    void update(GroupForm form);

    /**
     * 更新群组状态
     * @param group
     */
    void updateStatus(Group group);

    void change(GroupChangeForm form);
    /**
     * 查询账号(type 0:管理员 1:拉手号)
     * @param data
     * @param type
     * @return
     */
    List<Account> queryAccount(String data,Integer type);

    /**
     * 添加账号(tpye 0:管理员 1:拉手号)
     * @param form
     */
    void addAccount(GroupChangeAccountForm form);
    /**
     * 获取群组的管理员列表
     * @param groupId
     */
    List<Account> getAdmin(Long groupId);
    /**
     * 获取群组的拉手号列表
     * @param groupId
     */
    List<Account> getLa(Long groupId);

    /**
     * 获取群组的详细信息
     * @param groupId
     */
    GroupInfoDto getGroupInfo(Long groupId);

    /**
     * 上传群组的截图
     * @param form
     */
    void uploadGroup(UploadGroup form);

    /**
     * 获取拉手号的信息
     * @param accountIdList
     */
    LaDto getLaInfo(List<String> accountIdList);

    void addGroupLaCount(Long groupId);

    void changeMobile(ChangeMobileForm form);

    void changeGroup(GroupQueryParam form);

    List<Group> getFinishList(GroupExportForm form);

    void changeStatus(GroupQueryParam form);
}
