package com.whatsapp.cms.modules.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.whatsapp.cms.modules.dto.GroupDto;
import com.whatsapp.cms.modules.entity.Mobile;
import com.whatsapp.cms.modules.mapper.MobileMapper;
import com.whatsapp.cms.modules.query.MobileQueryParam;
import com.whatsapp.cms.modules.service.IMobileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 料子 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Service
public class MobileServiceImpl extends ServiceImpl<MobileMapper, Mobile> implements IMobileService {

    @Autowired
    private MobileMapper mobileMapper;

    @Override
    public IPage<Mobile> queryPage(MobileQueryParam param) {
        LambdaQueryWrapper<Mobile> mobileLambdaQueryWrapper = new LambdaQueryWrapper<Mobile>()
                .eq(param.getOrderId() != null, Mobile::getOrderId, param.getOrderId())
                .eq(param.getGroupId() != null, Mobile::getGroupId, param.getGroupId())
                .eq(StringUtils.isNotBlank(param.getMobile()), Mobile::getMobile, param.getMobile())
                .eq(param.getIsUse() != null, Mobile::getIsUse, param.getIsUse())
                .eq(param.getIsShill() != null, Mobile::getIsShill, param.getIsShill())
                .eq(param.getIsAdmin() != null, Mobile::getIsAdmin, param.getIsAdmin())
                .eq(param.getIsJoin() != null, Mobile::getIsJoin, param.getIsJoin())
                .orderByDesc(Mobile::getId);

        return this.page(param.getPage(), mobileLambdaQueryWrapper);
    }

    @Override
    public List<String> filterMobile(GroupDto groupDto) {
        if (groupDto.getMemberCount() <= 0) {
            return null;
        }

        List<Mobile> mobileList = this.list(new LambdaQueryWrapper<Mobile>()
                .eq(Mobile::getGroupId, groupDto.getId()));

        List<String> adminAndShillList = mobileList.stream()
                .filter(mobile -> mobile.getIsAdmin() == 1 || mobile.getIsShill() == 1)
                .map(Mobile::getMobile)
                .collect(Collectors.toList());

        List<String> remainingList = mobileList.stream()
                .filter(mobile -> mobile.getIsAdmin() != 1 && mobile.getIsShill() != 1)
                .limit(groupDto.getMemberCount() - adminAndShillList.size())
                .map(Mobile::getMobile)
                .toList();

        adminAndShillList.addAll(remainingList);

        return adminAndShillList;
    }

    @Override
    public Long countMobileByGroupId(Long groupId) {
        return this.count(new LambdaQueryWrapper<Mobile>().eq(Mobile::getGroupId, groupId));
    }

    @Override
    public Map<Long, Long> countMobileByGroupIds(List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        List<Map<String, Object>> maps = mobileMapper.countByGroupIds(groupIds);
        Map<Long, Long> resultMap = new HashMap<>();
        for (Map<String, Object> result : maps) {
            Long groupId = (Long) result.get("groupId");
            Long count = (Long) result.get("count");
            resultMap.put(groupId, count);
        }
        return resultMap;
    }
}
