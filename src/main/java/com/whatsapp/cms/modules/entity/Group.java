package com.whatsapp.cms.modules.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 群组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@TableName("tb_group")
public class Group implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联任务ID
     */
    private Long orderId;

    /**
     * 群名
     */
    private String groupName;

    /**
     * 拉群账号id
     */
    private Long accountId;

    /**
     * 平台群ID
     */
    private String platformId;

    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 群成员数量
     */
    private Integer memberCount;

    /**
     * 平台创建时间
     */
    private LocalDateTime platformCreateTime;

    /**
     * 状态（0=待执行，1=进行中，2=已完成,3=失败,4=暂停）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private Integer groupNum;

    private LocalDateTime finishTime;

    private String imgUrl;

    private Integer laCount;

    private Long accountGroup;

    private String successHandler;

    private Integer maxLa;
    @TableField(exist = false)
    private Long mobileCount;
}
