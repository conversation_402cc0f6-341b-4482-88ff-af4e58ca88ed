package com.whatsapp.cms.modules.aws;

import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.transfer.s3.S3TransferManager;

import static software.amazon.awssdk.transfer.s3.SizeConstant.MB;

public class S3ClientFactory {

    public static final S3TransferManager TRANSFER_MANAGER = createCustonTm();
    public static final S3Client S3_CLIENT;

    public static final S3Presigner S3_PRESIGNER;

    private static S3TransferManager createCustonTm() {
        // snippet-start:[s3.tm.java2.s3clientfactory.create_custom_tm]
        S3AsyncClient s3AsyncClient =
                S3AsyncClient.crtBuilder()
                        .credentialsProvider(ProfileCredentialsProvider.create())
                        .region(Region.AP_EAST_1)
                        .targetThroughputInGbps(20.0)
                        .minimumPartSizeInBytes(8 * MB)
                        .build();

        S3TransferManager transferManager =
                S3TransferManager.builder()
                        .s3Client(s3AsyncClient)
                        .build();
        return transferManager;
    }

    private static S3TransferManager createDefaultTm() {
        // snippet-start:[s3.tm.java2.s3clientfactory.create_default_tm]
        S3TransferManager transferManager = S3TransferManager.create();
        // snippet-end:[s3.tm.java2.s3clientfactory.create_default_tm]
        return transferManager;
    }

    static {
        S3_CLIENT = S3Client.builder()
                .credentialsProvider(ProfileCredentialsProvider.create())
                .region(Region.AP_EAST_1)
                .build();
        S3_PRESIGNER = S3Presigner.builder()
                .region(Region.AP_EAST_1)
                .credentialsProvider(ProfileCredentialsProvider.create())
                .build();
    }
}
