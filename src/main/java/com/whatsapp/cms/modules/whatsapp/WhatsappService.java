package com.whatsapp.cms.modules.whatsapp;

import cn.hutool.core.util.RandomUtil;
import com.whatsapp.cms.common.exception.CustomException;
import com.whatsapp.cms.modules.entity.Account;
import it.auties.whatsapp.api.ClientType;
import it.auties.whatsapp.api.WebHistoryLength;
import it.auties.whatsapp.api.Whatsapp;
import it.auties.whatsapp.controller.Keys;
import it.auties.whatsapp.controller.Store;
import it.auties.whatsapp.model.chat.Chat;
import it.auties.whatsapp.model.chat.GroupMetadata;
import it.auties.whatsapp.model.companion.CompanionDevice;
import it.auties.whatsapp.model.contact.Contact;
import it.auties.whatsapp.model.contact.ContactJid;
import it.auties.whatsapp.model.info.MessageInfo;
import it.auties.whatsapp.model.message.model.MessageStatus;
import it.auties.whatsapp.model.signal.auth.UserAgent;
import it.auties.whatsapp.model.signal.auth.Version;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class WhatsappService {
    // private static final URI uri;
    private final static String proxyHost = "proxy.cakeip.app";
    private final static int proxyPort = 3000;
    private final static String username = "A2308001003P2308001003_107_0_0_FP1LV7_30_1";
    private final static String password = "rauIuqmDJMGh";

    // static {
    //     try {
    //         uri = new URI("socks5", username + ":" + password, proxyHost, proxyPort, null, null, null);
    //         log.info("代理信息:{} ", uri);
    //     } catch (URISyntaxException e) {
    //         throw new RuntimeException(e);
    //     }
    // }

    /**
     * 创建链接
     *
     * @param account 账号实体类
     * @return Whatsapp api
     */
    public static Whatsapp createClient(Account account) {
        Result result = getResult(account);
        boolean business = account.getIsBusiness() == 1;
        try {
            return Whatsapp.customBuilder()
                    .keys(result.keys)
                    .store(result.store)
                    .build()
                    .addLoggedInListener((node) -> System.out.println("登录成功: " + node))
                    .addDisconnectedListener((node) -> System.out.println("断开连接: " + node))
                    .connect()
                    .join();
        } catch (IllegalArgumentException e) {
            return Whatsapp.mobileBuilder()
                    .newConnection(result.mobile)
                    .business(business)
                    .registered()
                    .get()
                    .addLoggedInListener((node) -> System.out.println("登录成功: " + node))
                    .addDisconnectedListener((node) -> System.out.println("断开连接: " + node))
                    .connect()
                    .join();
        }
    }

    /**
     * 创建链接，没有监听器
     *
     * @param account 账号实体类
     * @return Whatsapp api
     */
    public static Whatsapp createClientNotListener(Account account) {
        Result result = getResult(account);
        boolean business = account.getIsBusiness() == 1;
        try {
            return Whatsapp.customBuilder()
                    .keys(result.keys())
                    .store(result.store())
                    .build()
                    .connect()
                    .join();
        } catch (IllegalArgumentException e) {
            return Whatsapp.mobileBuilder()
                    .newConnection(result.mobile())
                    .business(business)
                    .registered()
                    .get()
                    .connect()
                    .join();
        }
    }

    /**
     * 创建链接,使用代理
     */
    public static Whatsapp createClientWithProxy(Account account) {
        log.info("当前账号:{},使用代理创建链接", account.getPhoneCode() + account.getPhone());
        Result result = getResult(account);
        boolean business = account.getIsBusiness() == 1;
        Store store = result.store;
        try {
            return Whatsapp.customBuilder()
                    .keys(result.keys)
                    .store(store)
                    .build()
                    .addNodeSentListener((node) -> System.out.println("发送: " + node))
                    .addNodeReceivedListener((node) -> System.out.println("接收: " + node))
                    .connect()
                    .join();
        } catch (IllegalArgumentException e) {
            return Whatsapp.mobileBuilder()
                    .newConnection(result.mobile)
                    // .proxy(uri)
                    .business(business)
                    .registered()
                    .get()
                    .addNodeSentListener((node) -> System.out.println("发送: " + node))
                    .addNodeReceivedListener((node) -> System.out.println("接收: " + node))
                    .connect()
                    .join();
        }
    }

    private static Result getResult(Account account) {
        long mobile = Long.parseLong(account.getPhoneCode() + account.getPhone());
        String publicKey = account.getPublicKey();
        String privateKey = account.getPrivateKey();
        String msgPublicKey = account.getMsgPublicKey();
        String msgPrivateKey = account.getMsgPrivateKey();
        String registerId = account.getRegistrationId();
        UUID uuid = UUID.fromString(account.getUuid());
        Store store;
        CompanionDevice companionDevice = new CompanionDevice("Galaxy Note Edge", "Samsung", UserAgent.UserAgentPlatform.ANDROID, new Version("11.0.0"));
        if (account.getIsBusiness() == 0) {
            store = Store.of(uuid, mobile, ClientType.MOBILE).historyLength(WebHistoryLength.STANDARD);
        } else {
            store = Store.of(uuid, mobile, ClientType.MOBILE).business(true).historyLength(WebHistoryLength.STANDARD);
            store.business(true);
        }
        store.device(companionDevice).version(new Version("2.23.23.5"));
        Keys keys = Keys.of(uuid, mobile, Base64.getDecoder().decode(publicKey), Base64.getDecoder().decode(privateKey), Base64.getDecoder().decode(msgPublicKey), Base64.getDecoder().decode(msgPrivateKey), Base64.getDecoder().decode(registerId));
        return new Result(mobile, store, keys);
    }

    private record Result(long mobile, Store store, Keys keys) {
    }


    /**
     * 随机找一个人发送消息
     *
     * @param api     Whatsapp
     * @param message 信息
     */
    public static void sendRandomMessage(Whatsapp api, String message) {
        // 获取联系人
        Contact[] contacts = Arrays.stream(api.store().contacts().toArray(new Contact[0]))
                .filter(contact -> !contact.jid().equals(api.store().jid().toWhatsappJid()))
                .toArray(Contact[]::new);
        // 随机获取联系人
        Contact contact = RandomUtil.randomEle(contacts);
        log.info("随机获取的联系人是 = {}", contact.fullName());
        try {
            CompletableFuture<MessageInfo> messageInfoCompletableFuture = api.sendMessage(contact.jid(), message);
            MessageInfo messageInfo = messageInfoCompletableFuture.join();
            String name = api.store().name();
            MessageStatus status = messageInfo.status();
            if (status.name().equals("SERVER_ACK")) {
                log.info("{} 发送成功", name);
                log.info("发送消息的详情消息是 = {}", messageInfo.toJson());
            } else {
                log.error("{} 发送消息失败,详情消息{}", name, messageInfo.toJson());
            }
        } catch (Exception e) {
            log.error("发送消息失败,{}", e.getMessage());
        }
    }

    /**
     * 检查该账号的群组
     *
     * @param api Whatsapp
     * @return 群组的信息的集合
     */
    public static List<GroupMetadata> checkGroup(Whatsapp api) {
        Store store = api.store();
        List<GroupMetadata> groupMetadataList = new ArrayList<>();
        List<Chat> chats = store.chats();
        for (Chat chat : chats) {
            if (chat.isGroup()) {
                GroupMetadata groupMetadata = api.queryGroupMetadata(chat).join();
                groupMetadataList.add(groupMetadata);
            }
        }
        return groupMetadataList;
    }

    /**
     * 创建群组,成员来自联系人
     *
     * @param groupName 群组名字
     * @param sum       群组人数
     * @param api       Whatsapp
     * @return 群组信息
     */
    public static GroupMetadata creatGroup(String groupName, Integer sum, Whatsapp api) {

        Store store = api.store();
        ContactJid whatsappJid = store.jid().toWhatsappJid();
        Contact[] contacts = store.contacts().toArray(new Contact[0]);
        Contact[] filteredContacts = Arrays.stream(contacts)
                .filter(contact -> !contact.jid().equals(whatsappJid))
                .limit(sum)
                .toArray(Contact[]::new);
        try {
            GroupMetadata groupMetadata = api.createGroup(groupName, filteredContacts).join();
            System.out.println(groupMetadata.toString());
            return groupMetadata;
        } catch (Exception e) {
            log.error("创建群组失败,{}", e.getMessage());
            throw new CustomException("创建群组失败");
        }
    }

    /**
     * 根据手机号创建Jid
     *
     * @param phone 带国家码的手机号
     * @return whatsapp的Jid对象
     */
    public static ContactJid createJid(String phone) {
        // 判断手机号第一位
        char c = phone.charAt(0);
        if (c == '+') {
            phone = phone.substring(1);
        }
        return ContactJid.of(phone + "@s.whatsapp.net");
    }
}
