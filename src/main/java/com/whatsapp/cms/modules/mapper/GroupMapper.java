package com.whatsapp.cms.modules.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.whatsapp.cms.modules.entity.Group;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 群组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Mapper
public interface GroupMapper extends BaseMapper<Group> {

    /**
     * 根据管理员的账号id来获取群组
     * @param accountId
     * @return
     */
    Group getGroupByAccount(Long accountId);

    /**
     * 上传图片
     */
    void uploadImg(Long groupId,String url);

    void addGroupLaCount(Long groupId);

    void reduceGroupLaCount(Long groupId);
}
