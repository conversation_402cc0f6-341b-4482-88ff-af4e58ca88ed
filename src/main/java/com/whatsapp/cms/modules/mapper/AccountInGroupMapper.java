package com.whatsapp.cms.modules.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.whatsapp.cms.modules.dto.AccountOneDto;
import com.whatsapp.cms.modules.dto.LaDto;
import com.whatsapp.cms.modules.entity.AccountInGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 群组账号关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Mapper
public interface AccountInGroupMapper extends BaseMapper<AccountInGroup> {

    List<String> getAdminByGroupId(Long groupId);

    AccountOneDto getLaByGroupId(Long groupId);

}
