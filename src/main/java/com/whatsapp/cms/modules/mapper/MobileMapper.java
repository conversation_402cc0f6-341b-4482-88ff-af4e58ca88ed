package com.whatsapp.cms.modules.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.whatsapp.cms.modules.entity.Mobile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 料子 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Mapper
public interface MobileMapper extends BaseMapper<Mobile> {
    List<Map<String, Object>> countByGroupIds(@Param("groupIds") List<Long> groupIds);
}
