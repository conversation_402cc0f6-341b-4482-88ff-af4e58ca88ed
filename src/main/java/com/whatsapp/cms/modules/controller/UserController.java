package com.whatsapp.cms.modules.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.whatsapp.cms.modules.entity.User;
import com.whatsapp.cms.modules.form.UserLoginForm;
import com.whatsapp.cms.modules.service.IUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final IUserService userService;

    @PostMapping("login")
    public SaResult login(@RequestBody @Validated UserLoginForm form) {
        User user = userService.getOne(Wrappers.<User>lambdaQuery().eq(User::getUsername, form.getUsername()));
        if (user == null) {
            return SaResult.error("用户不存在");
        }
        if(!user.getPassword().equals(DigestUtils.sha256Hex(form.getPassword()))){
            return SaResult.error("用户名或密码不存在");
        }
        StpUtil.login(user.getId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        return SaResult.data(tokenInfo);
    }


    @GetMapping("info")
    public SaResult info() {
        User user = userService.getById(StpUtil.getLoginIdAsLong());
        return SaResult.data(user);
    }

    @GetMapping("logout")
    public SaResult logout() {
        StpUtil.logout();
        return SaResult.ok();
    }
}
