package com.whatsapp.cms.modules.controller;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.whatsapp.cms.common.entity.BasePageResult;
import com.whatsapp.cms.modules.entity.Mobile;
import com.whatsapp.cms.modules.query.MobileQueryParam;
import com.whatsapp.cms.modules.service.IMobileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 料子 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@RestController
@RequestMapping("/mobile")
public class MobileController {

    @Autowired
    private IMobileService mobileService;

    @GetMapping("/page")
    public SaResult page(MobileQueryParam param) {
        IPage<Mobile> result = mobileService.queryPage(param);

        return SaResult.data(BasePageResult.newInstance(result));
    }

}
