package com.whatsapp.cms.modules.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode
public class GroupExportExcel {
    @ExcelProperty("群名称")
    @ColumnWidth(50)
    private String groupName;
    @ExcelProperty("完成时间")
    @ColumnWidth(50)
    private Date finishTime;
    @ExcelProperty("群成员数量")
    @ColumnWidth(50)
    private Integer groupNum;
    // @ExcelProperty("标签")
    // @ColumnWidth(50)
    // private String tag;
}
