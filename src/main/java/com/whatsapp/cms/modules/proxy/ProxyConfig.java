package com.whatsapp.cms.modules.proxy;

import java.net.Proxy;

/**
 * <AUTHOR>
 */
public class ProxyConfig {

    private Proxy proxy;

    private String username;

    private String password;

    public ProxyConfig(Proxy proxy) {
        this.proxy = proxy;
    }

    public ProxyConfig(Proxy proxy, String username, String password) {
        this.proxy = proxy;
        this.username = username;
        this.password = password;
    }

    @Override
    public String toString() {
        return String.format("代理IP：%s,账号密码：%s/%s", proxy.toString(), username, password);
    }

    public Proxy getProxy() {
        return proxy;
    }

    public void setProxy(Proxy proxy) {
        this.proxy = proxy;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
