package com.whatsapp.cms.modules.proxy;

import java.net.Proxy;

/**
 * <AUTHOR>
 */
public interface ProxyService {

    /**
     * 帐密模式获取代理
     *
     * @return
     */
    Proxy getProxyByAccountModel();

    /**
     * 获取用户名
     *
     * @return
     */
    String getUsername(String countryCode, Integer minutes, String key);

    /**
     * 获取密码
     *
     * @return
     */
    String getPassword();

    /**
     * 通过手机区号获取国家代码
     *
     * @param phoneCode
     * @return
     */
    String getCountryCodeByPhoneCode(String phoneCode);

    /**
     * 获取代理设置
     *
     * @param phoneCode
     * @param minutes
     * @param randomKey
     * @return
     */
    ProxyConfig getProxyConfig(String phoneCode, Integer minutes, String randomKey);

    String getHost();

    int getPort();
}
