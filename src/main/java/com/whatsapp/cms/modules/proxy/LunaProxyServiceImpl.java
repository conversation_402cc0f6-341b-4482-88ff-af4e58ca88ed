package com.whatsapp.cms.modules.proxy;

import cn.hutool.core.util.RandomUtil;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.whatsapp.cms.common.exception.CustomException;
import com.whatsapp.cms.common.utils.PhoneUtils;
import com.whatsapp.cms.modules.enums.CountryCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class LunaProxyServiceImpl implements ProxyService {

    public static final String HOST = "as.lunaproxy.com";

    public static final int PORT = 12233;

    public static final String PASSWORD = "AAaa112233";

    @Override
    public Proxy getProxyByAccountModel() {
        return new Proxy(Proxy.Type.HTTP, new InetSocketAddress(HOST, PORT));
    }

    @Override
    public String getUsername(String countryCode, Integer minutes, String key) {
        if (StringUtils.isBlank(key)) {
            key = RandomUtil.randomString(6);
        }else{
            key = PhoneUtils.randomByPhone(key);
        }
        PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
        String countryName;
        countryName = phoneNumberUtil.getRegionCodeForCountryCode(Integer.parseInt(countryCode)).toUpperCase();
        if (!countryName.equals("ZA") && !countryName.equals("ID") && !countryName.equals("US") && !countryName.equals("GB")) {
            countryName = "US";
        }
        if (StringUtils.isNotBlank(countryName)) {
            return String.format("user-lu3387629-region-%s-sessid-%s-sesstime-%s", countryName, key, minutes);
        } else {
            log.error("【设置代理】Luna没有找到对应的代理，国家编码为{}", countryCode);
            throw new CustomException("国家码:" + countryCode + "找不到对应的缩写");
        }

    }

    @Override
    public String getPassword() {
        return PASSWORD;
    }

    @Override
    public String getCountryCodeByPhoneCode(String phoneCode) {
        return PhoneUtils.getCountryIdByPhoneCode(phoneCode);
    }

    @Override
    public ProxyConfig getProxyConfig(String phoneCode, Integer minutes, String randomKey) {
        String countryCode = this.getCountryCodeByPhoneCode(phoneCode);
        return new ProxyConfig(getProxyByAccountModel(), getUsername(countryCode, minutes, randomKey), getPassword());
    }

    @Override
    public String getHost() {
        return HOST;
    }

    @Override
    public int getPort() {
        return PORT;
    }
}
