package com.whatsapp.cms.modules.dto;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.whatsapp.cms.common.utils.ProxyUtils;
import com.whatsapp.cms.modules.entity.Config;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AccountDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 手机区号
     */
    private String phoneCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 分组
     */
    private Long groupId;

    /**
     * 状态(1=正常，2=封禁，3=异常，4=登录过期）
     */
    private Integer status;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 使用状态（0=未使用，1=已使用）
     */
    private Integer useStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标签
     */
    private String tag;

    /**
     * 最后操作时间
     */
    private LocalDateTime lastOpsTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 消息公钥
     */
    private String msgPublicKey;

    /**
     * 消息私钥
     */
    private String msgPrivateKey;

    /**
     * 注册ID
     */
    private String registrationId;
    /**
     * uuid标识
     */
    private String uuid;
    /**
     * 是否企业账户(0-普通账户 1-企业账户)
     */
    private Integer isBusiness;

    private String downloadLink;

    private String env;

    private JSONObject groupInfo;

    /**
     * 账号类型(0-普通  1-管理员)
     */
    private Integer accountType;

    private String accountGroupName;

    public void setDownloadLink(String downloadLink) {
        this.downloadLink = downloadLink + "ws/api/account/download?accountId=" + this.id;
    }

    public void setEnv(Config config) {
        ProxyUtils proxyUtils = new ProxyUtils();
        this.env = proxyUtils.env(this.getPhoneCode(), config, this.getPhone());;
    }
}