package com.whatsapp.cms.modules.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class GroupInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联任务ID
     */
    private Long orderId;

    /**
     * 群名
     */
    private String groupName;

    /**
     * 拉群账号id
     */
    private Long accountId;

    /**
     * 平台群ID
     */
    private String platformId;

    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 群成员数量
     */
    private Integer memberCount;

    /**
     * 平台创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime platformCreateTime;

    /**
     * 状态（0=待执行，1=进行中，2=已完成,3=失败）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    private List<String> admin;

    private List<String> shill;

    private Long accountGroup;

    private Integer maxLa;
}
