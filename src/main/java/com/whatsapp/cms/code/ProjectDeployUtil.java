package com.whatsapp.cms.code;

import com.jcraft.jsch.*;
import com.whatsapp.cms.modules.enums.DeployEnum;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.Scanner;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class ProjectDeployUtil {


    public static void main(String[] args) throws JSchException, SftpException, IOException, InterruptedException {


        Properties properties = new Properties();
        properties.load(ProjectDeployUtil.class.getClassLoader().getResourceAsStream("application.yml"));
        String active = properties.getProperty("active");
        DeployEnum deployEnumByActive = DeployEnum.getDeployEnumByActive(active);
        System.out.println("开始部署环境：" + deployEnumByActive.getActive());
        System.out.println("服务器ip：" + deployEnumByActive.getHost());
        System.out.println("服务器密码：" + deployEnumByActive.getPassword());
        System.out.println("是否继续部署？(y/n)");
        Scanner scanner = new Scanner(System.in);
        if (!"y".equals(scanner.nextLine())) {
            System.out.println("部署取消");
            return;
        }
        serverDeploy(deployEnumByActive.getHost(), deployEnumByActive.getPassword());
    }

    private static void serverDeploy(String host, String password) throws JSchException, SftpException, IOException, InterruptedException {
        String username = "root";
        System.out.println("开始连接服务器...");
        JSch jsch = new JSch();
        Session session = jsch.getSession(username, host, 22);
        session.setPassword(password);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();
        System.out.println("服务器连接成功");
        ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
        sftpChannel.connect();
        long startTime = System.currentTimeMillis();
        sftpChannel.put(System.getProperty("user.dir") + "\\target\\ws.jar", "/home/<USER>/ws.jar", new SftpProgressMonitor() {

            private long uploadSize = 0L;

            private ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

            @Override
            public void init(int i, String s, String s1, long l) {
                System.out.println("开始上传" + s + "至远程" + s1 + ",文件总大小" + l / 1024 + "KB");
                executorService.scheduleWithFixedDelay(() -> {
                    System.out.println("已上传" + uploadSize / 1024 + "/" + l / 1024 + "KB");
                }, 1, 2, TimeUnit.SECONDS);
            }

            @Override
            public boolean count(long l) {
                uploadSize += l;
                return l > 0;
            }

            @Override
            public void end() {
                long endTime = System.currentTimeMillis();
                System.out.println("jar包上传成功，耗时" + (endTime - startTime) / 1000 + "s");
                executorService.shutdown();
            }
        }, ChannelSftp.OVERWRITE);
        sftpChannel.disconnect();
        System.out.println("开始执行启动脚本...");
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand("source /etc/profile&&sh /home/<USER>/admin_deploy.sh restart");
        channel.connect();
        InputStream in = channel.getInputStream();
        byte[] tmp = new byte[1024];
        while (true) {
            while (in.available() > 0) {
                int i = in.read(tmp, 0, 1024);
                if (i < 0) {
                    break;
                }
                System.out.print(new String(tmp, 0, i));
            }
            if (channel.isClosed()) {
                System.out.println("exit-status: " + channel.getExitStatus());
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (Exception ee) {
            }
        }
        channel.disconnect();
        session.disconnect();
    }


}
