package com.lifeng.common.exception;

import cn.dev33.satoken.exception.NotLoginException;
import com.lifeng.common.utils.MessageUtils;
import com.lifeng.common.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.annotation.Resource;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @Resource
    private MessageUtils messageUtils;

    @ExceptionHandler(CustomException.class)
    public Result<Void> bizExceptionHandler(CustomException e) {
        log.error("【业务异常】: {}", e.getMessage());
        return Result.failure(1, e.getMessage());
    }

    @ExceptionHandler(NotLoginException.class)
    public Result<Void> notLoginExceptionExceptionHandler(NotLoginException e) {
        log.error("【登录异常】: {}", ExceptionUtils.getStackTrace(e));
        return Result.failure(401, e.getMessage());
    }

    @ExceptionHandler(BindException.class)
    public Result<Void> bindExceptionHandler(BindException e) {
        log.error("【参数异常】: {}", ExceptionUtils.getStackTrace(e));
        BindingResult result = e.getBindingResult();
        if (result instanceof BeanPropertyBindingResult) {
            String msg = result.getAllErrors().get(0).getDefaultMessage();
            return Result.failure(1, msg);
        } else {
            return Result.failure(500, messageUtils.getMessage("system.error"));
        }
    }
    /**
     * 全局异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> exceptionHandler(Exception e) {
        log.error("【系统异常】: {}", ExceptionUtils.getStackTrace(e));
        // 处理异常
         if (e instanceof MethodArgumentNotValidException) {
            // 参数检验异常
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) e;
            BindingResult result = methodArgumentNotValidException.getBindingResult();
            return Result.failure(500, result.getFieldErrors().get(0).getDefaultMessage());
        } else if (e instanceof HttpRequestMethodNotSupportedException) {
            return Result.failure(500, messageUtils.getMessage("system.method.not.supported"));
        } else if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException ex = (MissingServletRequestParameterException) e;
            return Result.failure(500, messageUtils.getMessage("system.param.missing", new Object[]{ex.getParameterName()}));
        } else if (e instanceof MethodArgumentTypeMismatchException) {
            MethodArgumentTypeMismatchException ex = (MethodArgumentTypeMismatchException) e;
            return Result.failure(500, messageUtils.getMessage("system.param.type.mismatch", new Object[]{ex.getName()}));
        } else if (e instanceof NoHandlerFoundException) {
            NoHandlerFoundException ex = (NoHandlerFoundException) e;
            return Result.failure(500, messageUtils.getMessage("system.url.not.found", new Object[]{ex.getRequestURL()}));
        } else {
            log.error("【系统异常】: {}", ExceptionUtils.getStackTrace(e));
            return Result.failure(500, messageUtils.getMessage("system.error"));
        }
    }
}
