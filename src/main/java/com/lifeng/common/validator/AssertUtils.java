/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.lifeng.common.validator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.lifeng.common.exception.CustomException;
import com.lifeng.common.utils.MessageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 校验工具类
 *
 * @<NAME_EMAIL>
 * @since 1.0.0
 */
public class AssertUtils {
    /**
     * 获取国际化消息工具类
     */
    private static MessageUtils getMessageUtils() {
        return SpringUtil.getBean(MessageUtils.class);
    }

    public static void isBlank(String str) {
        if (StrUtil.isBlank(str)) {
            throw new CustomException(getMessageUtils().getMessage("validation.notBlank"));
        }
    }

    public static void isNull(Object object) {
        if (object == null) {
            throw new CustomException(getMessageUtils().getMessage("validation.notNull"));
        }
    }

    public static void isArrayEmpty(Object[] array) {
        if(ArrayUtil.isEmpty(array)){
            throw new CustomException(getMessageUtils().getMessage("validation.notEmpty"));
        }
    }

    public static void isListEmpty(List<?> list) {
        if(CollUtil.isEmpty(list)){
            throw new CustomException(getMessageUtils().getMessage("validation.notEmpty"));
        }
    }

    public static void isMapEmpty(Map map) {
        if(MapUtil.isEmpty(map)){
            throw new CustomException(getMessageUtils().getMessage("validation.notEmpty"));
        }
    }


    /**
     * 判断字符串是否为空
     *
     * @param str
     * @param message 提示信息
     * @param otherMessages 其他提示信息
     */
    public static void isBlank(String str, String message, String... otherMessages) {
        if (StringUtils.isBlank(str)) {
            throw new CustomException(handleMessage(message, otherMessages));
        }
    }


    public static void isNull(Object obj, String message, String... otherMessages) {
        if (null == obj) {
            throw new CustomException(handleMessage(message, otherMessages));
        }
    }




    /**
     * 判断是否错误
     *
     * @param isWrong
     * @param message 提示信息
     * @param otherMessages 其他提示信息
     */
    public static void check(boolean isWrong, String message, String... otherMessages) {
        if (isWrong) {
            //参数错误，不要打印堆栈
            throw new CustomException(handleMessage(message, otherMessages));
        }
    }

    /**
     * 判断对象是否为空
     *
     * @param collection
     * @param message 提示信息
     * @param otherMessages 其他提示信息
     */
    public static void isEmpty(Collection collection, String message, String... otherMessages) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new CustomException(handleMessage(message, otherMessages));
        }
    }

    /**
     * 处理提示信息
     * @param message 提示信息
     * @param otherMessages 其他提示信息
     * @return
     */
    private static String handleMessage(String message, String... otherMessages) {
        if(ArrayUtil.isNotEmpty(otherMessages)) {
            message = message + ":"  + ArrayUtil.join(otherMessages, ",");

        }

        return message;
    }
}