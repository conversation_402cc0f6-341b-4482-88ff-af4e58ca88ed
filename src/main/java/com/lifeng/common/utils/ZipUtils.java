package com.lifeng.common.utils;

import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtils {

    public static void putFile(ZipOutputStream zip, String path, String data) throws IOException {
        zip.putNextEntry(new ZipEntry(path));
        IOUtils.write(data, zip, "UTF-8");
        zip.closeEntry();
    }
}
