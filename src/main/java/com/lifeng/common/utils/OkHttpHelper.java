package com.lifeng.common.utils;

import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

public class OkHttpHelper {

    private static volatile OkHttpClient singleton;

    public static final int MAX_REQUEST = 3000;

    private OkHttpHelper() {

    }

    public static OkHttpClient getInstance() {
        if (singleton == null) {
            synchronized (OkHttpHelper.class) {
                if (singleton == null) {
                    Dispatcher dispatcher = new Dispatcher();
                    dispatcher.setMaxRequests(MAX_REQUEST);
                    dispatcher.setMaxRequestsPerHost(MAX_REQUEST);
                    singleton = new OkHttpClient
                            .Builder()
                            .dispatcher(new Dispatcher())
                            .followRedirects(false)
                            .connectionPool(new ConnectionPool(MAX_REQUEST, 30, TimeUnit.MILLISECONDS))
                            .writeTimeout(30, TimeUnit.SECONDS)
                            .readTimeout(30, TimeUnit.SECONDS)
                            .retryOnConnectionFailure(true)
                            .build();
                }
            }
        }
        return singleton;
    }
}
