package com.lifeng.common.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolHelper {

    private static volatile ThreadPoolExecutor orderExecutor;

    private static volatile ThreadPoolExecutor accountExecutor;

    private ThreadPoolHelper() {

    }

    public static ThreadPoolExecutor getChatOrderInstance() {
        if (orderExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (orderExecutor == null) {
                    orderExecutor = new ThreadPoolExecutor(
                            2000,
                            2000,
                            1,
                            TimeUnit.MINUTES,
                            new LinkedBlockingQueue<>(),
                            new ThreadFactoryBuilder().setNameFormat("chat-order-pool-%d").build(),
                            new ThreadPoolExecutor.AbortPolicy()
                    );
                }
            }
        }
        return orderExecutor;
    }

    public static ThreadPoolExecutor getAccountInstance() {
        if (accountExecutor == null) {
            synchronized (ThreadPoolHelper.class) {
                if (accountExecutor == null) {
                    accountExecutor = new ThreadPoolExecutor(
                            1500,
                            1500,
                            1,
                            TimeUnit.MINUTES,
                            new LinkedBlockingQueue<>(),
                            new ThreadFactoryBuilder().setNameFormat("account-pool-%d").build(),
                            new ThreadPoolExecutor.AbortPolicy()
                    );
                }
            }
        }
        return accountExecutor;
    }
}
