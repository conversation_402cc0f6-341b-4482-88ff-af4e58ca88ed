package com.lifeng.common.utils;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;

@Slf4j
public class OkHttpUtils {

    static OkHttpClient HTTP_CLIENT = OkHttpHelper.getInstance();

    public static final MediaType JSON
            = MediaType.get("application/json; charset=utf-8");

    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
                .get()
                .url(url)
                .build();

        return synchronizedCall(request);
    }

    public static String get(String url, Headers headers) throws IOException {
        Request request = new Request.Builder()
                .get()
                .headers(headers)
                .url(url)
                .build();

        return synchronizedCall(request);
    }

    public static <T> T get(String url, Class<T> clz) throws IOException {
        return parse(get(url), clz);
    }

    public static String post(String url, String json) throws IOException {
        RequestBody requestBody = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .post(requestBody)
                .addHeader("X-CUSTOMER-NAME", "667026478491472918")
                .url(url)
                .build();
        return synchronizedCall(request);
    }

    public static <T> T post(String url, String json, Class<T> clz) throws IOException {
        return parse(post(url, json), clz);
    }

    public static String getRedirectUrl(String url) {
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        try {
            try (Response response = HTTP_CLIENT.newCall(request).execute()) {
                return response.headers().get("Location");
            }
        } catch (IOException e) {
            return null;
        }
    }

    private static String synchronizedCall(Request request) throws IOException {
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            return response.body().string();
        }
    }

    private static <T> T synchronizedCall(Request request, Class<T> clz) throws IOException {
        return parse(synchronizedCall(request), clz);
    }

    public static <T> T parse(String json, Class<T> clz) {
        return JSONObject.parseObject(json, clz);
    }
}
