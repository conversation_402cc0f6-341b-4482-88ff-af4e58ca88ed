package com.lifeng.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PageParam {

    @Schema(description = "当前页")
    private Long current;

    @Schema(description = "每页大小")
    private Long pageSize;

    @JsonIgnore
    public <T> IPage<T> getPage() {
        if (current == null) {
            current = 1L;
        }
        if (pageSize == null) {
            pageSize = 20L;
        }
        return new Page<>(current, pageSize);
    }
}
