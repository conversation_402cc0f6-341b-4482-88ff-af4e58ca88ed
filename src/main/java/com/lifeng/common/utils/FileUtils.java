package com.lifeng.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.lifeng.common.exception.CustomException;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileUtils {
    private FileUtils() {
    }


    /**
     * 获取多个文件的http地址
     *
     * @param baseUrl  http的开头地址
     * @param fileUrls 多个文件的相对地址
     * @return
     */
    public static List<String> getFileFullUrls(String baseUrl, String fileUrls) {
        List<String> urls = new ArrayList<>();

        if (StrUtil.isBlank(fileUrls)) {
            return urls;
        }

        String[] arrays = fileUrls.split(",");

        for (String row : arrays) {
            urls.add(getFileFullUrl(baseUrl, row));
        }

        return urls;
    }

    /**
     * 获取文件的http地址
     *
     * @param baseUrl http的开头地址
     * @param fileUrl 文件的相对地址
     * @return
     */
    public static String getFileFullUrl(String baseUrl, String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            return fileUrl;
        }

        if (fileUrl.startsWith("http")) {
            return fileUrl;
        }

        return baseUrl + File.separator + fileUrl;
    }

    /**
     * 替换掉文件的http地址的前缀
     *
     * @param fileFullUrl 文件的http地址
     * @param baseUrl     http的开头地址
     * @return
     */
    public static String replaceBaseUrl(String fileFullUrl, String baseUrl) {
        if (StrUtil.isBlank(fileFullUrl)) {
            return fileFullUrl;
        }

        return fileFullUrl.replace(baseUrl + File.separator, "");
    }


    /**
     * 获取文件的文件夹路径
     *
     * @param basePath 根目录
     * @param filePath 文件相对路径
     * @return
     */
    public static String getFileFullPath(String basePath, String filePath) {
        return basePath + File.separator + File.separator + filePath;
    }

    /**
     * 上传文件
     *
     * @param basePath 根目录
     * @param dir      文件夹枚举
     * @param file     文件数据
     * @return 相对路径的地址
     */
    public static String uploadFile(String basePath, String dir, MultipartFile file) {
        String name = file.getOriginalFilename();
        String fileType = name.substring(name.lastIndexOf("."));

        if(StrUtil.isBlank(fileType)){
            return null;
        }

        //生成文件名称
        String fileName = createFileName(fileType);
        //本地上传的路径
        String directory = basePath + File.separator + dir;

        File targetFile = new File(directory, fileName);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }

        try {
            file.transferTo(targetFile);

        } catch (IOException e) {
            throw new CustomException("图片上传失败", e);
        }

        return dir + File.separator + fileName;

    }

    /**
     * 上传文件
     *
     * @param basePath 根目录
     * @param dir      文件夹枚举
     * @param file     文件数据
     * @return
     * @throws Exception
     */
    public static String uploadFile(String basePath, String dir, byte[] file) throws Exception {
        //生成文件名称
        String imageName = createFileName(".png");
        //本地上传的路径
        String directory = basePath + File.separator + dir;

        File targetFile = new File(directory);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }

        FileOutputStream out = new FileOutputStream(directory + File.separator + imageName);
        out.write(file);
        out.flush();
        out.close();

        return dir + File.separator + imageName;

    }


    /**
     * 保存http域名地址的文件
     *
     * @param basePath    根目录
     * @param dir         文件夹枚举
     * @param fileFullUrl 文件的http全地址
     * @return
     */
    public static String saveByFullUrl(String basePath, String dir, String fileFullUrl) {
        if (StrUtil.isBlank(fileFullUrl)) {
            return null;
        }

        FileOutputStream outStream = null;

        try {
            //new一个URL对象
            URL url = new URL(fileFullUrl);
            //打开链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置请求方式为"GET"
            conn.setRequestMethod("GET");
            //超时响应时间为5秒
            conn.setConnectTimeout(5 * 1000);
            //通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            //得到图片的二进制数据，以二进制封装得到数据，具有通用性
            byte[] data = readInputStream(inStream);

            //生成文件名称
            String imageName = createFileName(".png");
            //本地上传的路径
            String imageFullPath = basePath + File.separator + dir + File.separator + imageName;

            //new一个文件对象用来保存图片，默认保存当前工程根目录
            File imageFile = new File(imageFullPath);
            //创建输出流
            outStream = new FileOutputStream(imageFile);
            //写入数据
            outStream.write(data);

            return dir + File.separator + imageName;


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != outStream) {
                //关闭输出流
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    /**
     * 删除文件
     *
     * @param fileFullPath 文件全路径
     * @return
     */
    public static boolean deleteFile(String fileFullPath) {
        boolean flag = false;

        File file = new File(fileFullPath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            file.delete();
            flag = true;
        }
        return flag;
    }

    public static Set<String> getTxtFileLines(String fullPath) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(fullPath), StandardCharsets.UTF_8))) {
            Set<String> uniqueLines = new HashSet<>();
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    uniqueLines.add(line.trim());
                }
            }

            return uniqueLines;
        } catch (IOException e) {
            e.printStackTrace();
            throw new CustomException("解析txt文件失败", e);
        }

    }


    /**
     * 读取文件流
     *
     * @param inStream
     * @return
     * @throws Exception
     */
    private static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        //使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        //关闭输入流
        inStream.close();
        //把outStream里的数据写入内存
        return outStream.toByteArray();
    }


    /**
     * 生成UUID规则的文件名称
     * @param fileType 文件类型 如.png
     * @return
     */
    public static String createFileName(String fileType) {
        return UUID.randomUUID().toString().replaceAll("-", "") + fileType;
    }


    /**
     * 是否base64编码
     *
     * @param base64
     * @return
     */
    public static boolean isBase64(String base64) {
        if (StrUtil.isBlank(base64)) {
            return false;
        }

        String[] data = base64.split(",");

        if (2 != data.length || !data[0].startsWith("data:image/")) {
            return false;
        }

        return true;
    }

    /**
     * 处理Base64解码并写图片到指定位置
     *
     * @param basePath 根目录
     * @param base64   图片Base64数据
     * @return 数组，第一个元素为图片名称，第二个元素为完整图片路径
     */
    public static String base64ToImage(String basePath, String dir, String base64) {


        String[] data = base64.split(",");

        if (2 != data.length || !data[0].startsWith("data:image/")) {
            return null;
        }

        //base64数据
        String imageString = data[1];

        BufferedImage image;

        ByteArrayInputStream bis = null;

        //生成文件名称
        String imageName = createFileName(".png");
        //本地上传的路径
        String imageFullPath = basePath + File.separator + dir + File.separator + imageName;

        try {
            byte[] imageByte = Base64.decode(imageString);
            bis = new ByteArrayInputStream(imageByte);
            image = ImageIO.read(bis);

            File outputFile = new File(imageFullPath);

            ImageIO.write(image, "png", outputFile);

            return dir + File.separator + imageName;


        } catch (IOException e) {
            e.printStackTrace();

        } finally {
            if (null != bis) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }


        }

        return null;
    }


    /**
     * 通过浏览器下载文件
     *
     * @param response     响应数据
     * @param fileName     文件名称
     * @param fileFullPath 目标文件路径
     */
    public static void download(HttpServletResponse response, String fileName, String fileFullPath) {
        OutputStream os = null;
        try {
            File file = new File(fileFullPath);
            if (!file.exists()) {
                return;
            }
            os = response.getOutputStream();
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream; charset=utf-8");
            os.write(FileUtil.readBytes(file));
        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    /**
     * 通过浏览器下载文件
     *
     * @param response     响应数据
     * @param file 文件
     */
    public static void download(HttpServletResponse response, File file) {
        OutputStream os = null;
        try {

            os = response.getOutputStream();
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
            response.setContentType("application/octet-stream; charset=utf-8");

            os.write(FileUtil.readBytes(file));
        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            IOUtils.closeQuietly(os);
        }
    }


    /**
     * 下载压缩文件
     *
     * @param zipName       压缩文件名称
     * @param response
     * @param fileUrls      文件路径
     * @param isNetworkFile 是否网络文件，true为网络文件
     */
    public static void downloadZip(String zipName, HttpServletResponse response, List<String> fileUrls, Boolean isNetworkFile) {
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(response.getOutputStream());
            zipName = URLEncoder.encode(zipName + ".zip", "UTF-8");//转换中文否则可能会产生乱码
            response.setContentType("application/octet-stream; charset=utf-8");// 指明response的返回对象是文件流
            response.setHeader("Content-Disposition", "attachment;filename=" + zipName);// 设置在下载框默认显示的文件名
            for (String fileUrl : fileUrls) {
                String[] splits = fileUrl.split("/");
                String fileName = splits[splits.length - 1];

                zos.putNextEntry(new ZipEntry(fileName));
                InputStream fis;
                if (!isNetworkFile) {
                    fis = new FileInputStream(new File(fileUrl));
                } else {
                    URL url = new URL(fileUrl);
                    fis = url.openConnection().getInputStream();
                }
                byte[] buffer = new byte[2048];
                int len = 0;
                while ((len = fis.read(buffer)) != -1) {
                    zos.write(buffer, 0, len);
                }
                zos.closeEntry();
                fis.close();
            }
        } catch (Exception e) {
            throw new CustomException("zip error", e);
        } finally {
            IOUtils.closeQuietly(zos);
        }
    }
}
