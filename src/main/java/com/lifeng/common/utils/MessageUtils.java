package com.lifeng.common.utils;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Locale;

/**
 * 国际化消息工具类
 * 用于在代码中获取国际化消息
 * 
 * <AUTHOR>
 */
@Component
public class MessageUtils {

    @Resource
    private MessageSource messageSource;

    /**
     * 获取国际化消息
     * 
     * @param code 消息代码
     * @return 国际化消息
     */
    public String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * 获取国际化消息
     * 
     * @param code 消息代码
     * @param args 消息参数
     * @return 国际化消息
     */
    public String getMessage(String code, Object[] args) {
        return getMessage(code, args, "");
    }

    /**
     * 获取国际化消息
     * 
     * @param code 消息代码
     * @param args 消息参数
     * @param defaultMessage 默认消息
     * @return 国际化消息
     */
    public String getMessage(String code, Object[] args, String defaultMessage) {
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, defaultMessage, locale);
    }

    /**
     * 获取国际化消息（指定语言环境）
     * 
     * @param code 消息代码
     * @param args 消息参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    public String getMessage(String code, Object[] args, Locale locale) {
        return messageSource.getMessage(code, args, "", locale);
    }

    /**
     * 获取当前语言环境
     * 
     * @return 当前语言环境
     */
    public Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }

    /**
     * 判断是否为中文环境
     * 
     * @return true-中文环境，false-其他环境
     */
    public boolean isChinese() {
        Locale locale = getCurrentLocale();
        return Locale.SIMPLIFIED_CHINESE.equals(locale) || 
               Locale.CHINESE.equals(locale) ||
               "zh".equals(locale.getLanguage());
    }

    /**
     * 判断是否为英文环境
     * 
     * @return true-英文环境，false-其他环境
     */
    public boolean isEnglish() {
        Locale locale = getCurrentLocale();
        return Locale.US.equals(locale) || 
               Locale.ENGLISH.equals(locale) ||
               "en".equals(locale.getLanguage());
    }
}