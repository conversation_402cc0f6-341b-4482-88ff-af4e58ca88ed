package com.lifeng.modules.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lifeng.common.utils.PageParam;
import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.RechargeOrderEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lifeng.modules.model.query.OrderQuery;
import com.lifeng.modules.model.response.RechargeOrderResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 充值订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface RechargeOrderMapper extends BaseMapper<RechargeOrderEntity> {

    IPage<RechargeOrderResp> selectRechargeOrderPage(@Param("page") IPage<?> page,@Param("query") OrderQuery pageParam,@Param("customerId")Long customerId);
}
