package com.lifeng.modules.mapper;

import com.lifeng.modules.model.entity.DictEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Mapper
public interface DictMapper extends BaseMapper<DictEntity> {

    List<Map<String, Object>> getDict(@Param("codes") List<String> codeList);
}
