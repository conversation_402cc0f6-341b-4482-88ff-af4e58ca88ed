package com.lifeng.modules.model.query;

import com.lifeng.common.utils.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.checkerframework.checker.units.qual.N;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@Schema(description = "广告户列表查询条件")
public class AdAccountListQuery extends PageParam {
    /**
     * 广告户状态（正常、封禁）、是否清零（是/否）
     * 广告户名称、广告户ID、时区、订单提交时间、完成下户时间
     */
    @Schema(description = "广告户状态 参考字典 ad_account_status")
    private Integer accountStatus;

    @Schema(description = "是否清零 参考字典 clear_order_status" )
    private Integer clearStatus;

    @Schema(description = "广告户名称")
    private String name;

    @Schema(description = "广告户ID")
    private String adAccountId;

    @Schema(description = "时区")
    private String timezone;

    @Schema(description = "开始 订单提交时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginRequirementTime;

    @Schema(description = "结束 订单提交时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endRequirementTime;


    @Schema(description = "开始 完成下户时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginFinishTime;

    @Schema(description = "结束 完成下户时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endFinishTime;

    @Schema(description = "分组ID")
    private Long groupId;

    @Schema(description = "下户订单状态  参考字典 ad_account_order_status")
    private Integer orderStatus;

    @Schema(description = "接收状态")
    private Boolean takeStatus;
}
