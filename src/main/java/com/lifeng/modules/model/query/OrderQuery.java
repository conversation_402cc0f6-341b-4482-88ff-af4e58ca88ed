package com.lifeng.modules.model.query;

import com.lifeng.common.utils.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 充值订单/清零订单/减款订单 订单管理查询条件
 */
@Data
@Schema(description = "订单查询条件")
public class OrderQuery extends PageParam {

    @Schema(description = "订单状态  2-处理中 3-已完成 ")
    private Integer status;
    @Schema(description = "广告户ID")
    private String adAccountId;
//    @Schema(description = "发起开始时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime beginHandleTime;
//    @Schema(description = "发起结束时间")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime endHandleTime;
    @Schema(description = "完成开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginFinishTime;
    @Schema(description = "完成结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endFinishTime;

    @Schema(description = "发起开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginCreateTime;
    @Schema(description = "发起结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endCreateTime;

}
