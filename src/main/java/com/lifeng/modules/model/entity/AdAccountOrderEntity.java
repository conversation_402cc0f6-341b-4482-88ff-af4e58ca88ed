package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 下户订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@Setter
@TableName("biz_ad_account_order")
public class AdAccountOrderEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 关联广告户
     */
    private String adAccountId;

    /**
     * 广告户名字
     */
    private String adAccountName;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 终止时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 授权时间
     */
    private LocalDateTime finishTime;

    /**
     * 回收时间
     */
    private LocalDateTime recycleTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 总消耗
     */
    private BigDecimal totalSpent;

    /**
     * 下户成本
     */
    private BigDecimal cost;

    /**
     * 上系列时间
     */
    private LocalDateTime startCampaignTime;

    /**
     * 是否已退款
     */
    private Boolean refunded;

    /**
     * 客户需求ID
     */
    private Long customerRequirementId;

    /**
     * 开启预充
     */
    private Boolean enablePrepay;

    /**
     * 是否一刀流
     */
    private Boolean isOneDollar;

    private Integer takeStatus;

    private Long groupId;
}
