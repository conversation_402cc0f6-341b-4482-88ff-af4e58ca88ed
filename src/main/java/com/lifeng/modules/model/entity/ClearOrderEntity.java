package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 清零订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
@Setter
@TableName("biz_clear_order")
public class ClearOrderEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 平台广告户ID
     */
    private String platformAdId;

    /**
     * 清零金额
     */
    private BigDecimal clearAmount;

    /**
     * 卡片余额
     */
    private BigDecimal cardBalance;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 卡台状态
     */
    private Integer cardStatus;

    /**
     * fb检测状态
     */
    private Integer fbCheckStatus;

    /**
     * 关联消息ID
     */
    private Integer applyMessageId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 凭证
     */
    private String certificate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 卡台清零结果
     */
    private String cardClearResult;

    /**
     * fb操作id
     */
    private String fbOpsId;
}
