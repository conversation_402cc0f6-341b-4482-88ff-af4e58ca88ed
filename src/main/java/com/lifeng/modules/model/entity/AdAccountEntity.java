package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 广告账号
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@Setter
@TableName("biz_ad_account")
public class AdAccountEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联BM5 ID
     */
    private Long businessManagerId;

    /**
     * 平台用户ID
     */
    private String platformAccountId;

    /**
     * 平台广告ID
     */
    private String platformAdId;

    /**
     * 广告户名称
     */
    private String name;

    /**
     * 完整卡号
     */
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    private String fuzzyCardNumber;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 账号状态
     */
    private Integer accountStatus;

    /**
     * 养号状态
     */
    private Integer keepStatus;

    /**
     * 出售状态
     */
    private Integer saleStatus;

    /**
     * 个号地区
     */
    private String personalArea;

    /**
     * 浏览器编号
     */
    private String browserNo;

    /**
     * 清零状态
     */
    private Integer clearStatus;

    /**
     * 花费限额
     */
    private BigDecimal spendCap;

    /**
     * 消耗
     */
    private BigDecimal amountSpent;

    /**
     * 剩余应付
     */
    private BigDecimal balance;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标签
     */
    private String tag;

    /**
     * 关联bm5 id
     */
    private String bmId;

    /**
     * 观察户浏览器
     */
    private String parentBrowserNo;

    /**
     * 总花费
     */
    private BigDecimal totalSpent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 申诉状态
     */
    private Integer appealStatus;

    /**
     * 账单国家
     */
    private String billCountry;

    /**
     * 账单货币
     */
    private String billCurrency;

    /**
     * bm5授权时间
     */
    private LocalDateTime bmAuthTime;

    /**
     * 封禁时间
     */
    private LocalDateTime banTime;

    /**
     * 出售时间
     */
    private LocalDateTime saleTime;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * bm1浏览器
     */
    private String bm1Browser;

    /**
     * 关联bm1 id
     */
    private Long bm1Id;

    /**
     * 是否剔除管理
     */
    private Boolean isRemoveAdmin;

    /**
     * 是否低限
     */
    private Boolean isLowLimit;

    /**
     * 真实限额
     */
    private BigDecimal realAdtrustDsl;

    /**
     * bm类型
     */
    private Integer bmItemType;

    /**
     * 坑位渠道ID
     */
    private Long bmItemChannelId;

    /**
     * vo状态
     */
    private Integer voStatus;

    /**
     * 请求头
     */
    private String headers;

    /**
     * 充值金余额
     */
    private BigDecimal prepayAccountBalance;

    /**
     * 浏览器ID
     */
    private String browserId;

    /**
     * 付费门槛
     */
    private BigDecimal billingThresholdCurrencyAmount;
}
