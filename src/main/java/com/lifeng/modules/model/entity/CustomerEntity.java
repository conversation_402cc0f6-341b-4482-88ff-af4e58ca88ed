package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
@Setter
@TableName("biz_customer")
@Builder
public class CustomerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联商务
     */
    private Long businessUserId;

    /**
     * 手续费百分比
     */
    private BigDecimal feeRatePercent;

    /**
     * 手续费扣款方式
     */
    private Integer feeHandleMethod;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * TG群ID
     */
    private Long telegramChatId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 机器人权限
     */
    private String robotPermission;

    /**
     * 开户费
     */
    private BigDecimal buyAccountFee;

    /**
     * 是否退款
     */
    private Boolean isRefund;

    /**
     * 结算模式
     */
    private Integer settleType;

    /**
     * 结算限额
     */
    private BigDecimal settleLimitAmount;

    /**
     * 预警限额
     */
    private BigDecimal warnLimitAmount;

    /**
     * 上一次结算消耗
     */
    private BigDecimal lastSettleSpent;

    /**
     * 客户状态：1-正常，2-终止
     */
    private Integer status;

    /**
     * 终止时间
     */
    private LocalDateTime terminateTime;

    /**
     * 是否是自用号
     */
    private Boolean isSelfAccount;

    /**
     * 客户类型：1-正式客户、2-潜在客户
     */
    private Integer type;

    /**
     * 客户来源
     */
    private Long sourceId;

    /**
     * 客户行业
     */
    private Integer industry;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户职位
     */
    private String customerPosition;

    /**
     * 城市
     */
    private String city;

    /**
     * 团队规模
     */
    private String teamSize;

    /**
     * 团队单日消耗(美元)
     */
    private String dailyTeamSpending;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
}
