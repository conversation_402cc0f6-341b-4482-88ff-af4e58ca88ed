package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客户需求
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@Setter
@TableName("biz_customer_requirement")
public class CustomerRequirementEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 需求时间
     */
    private LocalDateTime requirementTime;

    /**
     * 需求数量
     */
    private Integer quantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 状态:待接单，处理中，已完成，取消
     */
    private Integer status;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成数量
     */
    private Integer finishQuantity;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * bm类型
     */
    private Integer bmType;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 广告户名称
     */
    private String adAccountName;

    /**
     * 客户邮箱
     */
    private String customerEmail;
}
