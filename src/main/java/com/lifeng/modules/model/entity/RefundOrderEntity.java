package com.lifeng.modules.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 退款订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@Setter
@TableName("biz_refund_order")
public class RefundOrderEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户
     */
    private Long customerId;

    /**
     * 广告户ID
     */
    private String platformAdId;

    /**
     * 卡台状态
     */
    private Integer cardStatus;

    /**
     * fb检测状态
     */
    private Integer fbCheckStatus;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 飞机消息
     */
    private Integer applyMessageId;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 凭证
     */
    private String certificate;

    /**
     * 备注
     */
    private String remark;

    /**
     * fb操作id
     */
    private String fbOpsId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
