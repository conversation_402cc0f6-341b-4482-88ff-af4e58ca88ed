package com.lifeng.modules.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@Schema(description = "用户登录请求参数")
public class LoginReq {

    @Schema(description = "用户名")
    @NotBlank(message = "{login.username.notBlank}")
    @Size(min = 3, max = 50, message = "{login.username.size}")
    private String username;

    @Schema(description = "密码")
    @NotBlank(message = "{login.password.notBlank}")
    @Size(min = 6, max = 100, message = "{login.password.size}")
    private String password;


    @Schema(description = "验证码")
    @NotBlank(message = "{login.captcha.notBlank}")
    private String code;

    @Schema(description = "key")
    @NotBlank
    private String key;
}
