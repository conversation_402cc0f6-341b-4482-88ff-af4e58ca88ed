package com.lifeng.modules.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AdAccountRefundReq {

    @NotNull(message = "{adAccount.refund.amount.notNull}")
    @DecimalMin(value = "0", inclusive = false, message = "{adAccount.refund.amount.positive}")
    @Schema(description = "减款金额")
    private BigDecimal amount;

    @NotNull(message = "{adAccount.id.notNull}")
    @Schema(description = "广告户ID")
    private String adAccountId;

}
