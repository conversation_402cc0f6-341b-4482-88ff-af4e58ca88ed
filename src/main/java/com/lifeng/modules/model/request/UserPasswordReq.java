package com.lifeng.modules.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class UserPasswordReq {

    @NotBlank(message = "{validation.notBlank}")
    private String oldPassword;

    @NotBlank(message = "{validation.notBlank}")
    @Size(min = 6, max = 100, message = "{login.password.size}")
    private String newPassword;

    @NotBlank(message = "{validation.notBlank}")
    @Size(min = 6, max = 100, message = "{login.password.size}")
    private String confirmPassword;

}
