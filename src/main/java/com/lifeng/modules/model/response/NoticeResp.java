package com.lifeng.modules.model.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@Setter
public class NoticeResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;


    /**
     * 创建时间
     */
    @Schema(description = "日期")
    private String time;


}
