package com.lifeng.modules.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CustomerRequirementResp {

    private Long id;

    @Schema(description = "状态 字典code：customer_requirement_status")
    private Integer status;

    @Schema(description = "广告户名称")
    private String adAccountName;

    @Schema(description = "客户BM ID")
    private String customerBmId;

    @Schema(description = "时区")
    private String timezone;

    @Schema(description = "客户邮箱")
    private String customerEmail;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "实际下户数量")
    private Integer finishQuantity;

    @Schema(description = "订单提交时间")
    private LocalDateTime requirementTime;


}
