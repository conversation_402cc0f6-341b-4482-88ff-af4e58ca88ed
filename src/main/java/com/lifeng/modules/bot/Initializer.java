package com.lifeng.modules.bot;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

@Component
@Slf4j
@RequiredArgsConstructor
public class Initializer {

    private final MyTeleBot myTeleBot;
    private final BotConfig botConfig;

    @EventListener({ApplicationReadyEvent.class})
    public void init() {
        // try {
        //     log.info("正在初始化Telegram机器人...");
        //     log.info("机器人用户名: {}", botConfig.getUsername());
        //     TelegramBotsApi telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
        //     telegramBotsApi.registerBot(myTeleBot);
        //     log.info("Telegram机器人启动成功！");
        // } catch (TelegramApiException e) {
        //     log.error("机器人启动失败: {}", e.getMessage(), e);
        // } catch (Exception e) {
        //     log.error("机器人初始化过程中发生未知错误: {}", e.getMessage(), e);
        // }
    }
}