package com.lifeng.modules.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.alibaba.fastjson2.JSONObject;
import com.lifeng.common.utils.Result;
import com.lifeng.modules.model.entity.CustomerEntity;
import com.lifeng.modules.model.request.LoginReq;
import com.lifeng.modules.model.request.UserPasswordReq;
import com.lifeng.modules.model.response.CustomerStatResp;
import com.lifeng.modules.service.ICustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final ICustomerService customerService;


    @Operation(summary = "用户登录",
            description = "用户登录接口，验证用户名和密码后返回token信息")
    @PostMapping("/login")
    public Result<JSONObject> login(
            @Parameter(description = "登录请求参数", required = true)
            @RequestBody @Validated LoginReq req) {

        CustomerEntity customer = customerService.login(req);
        StpUtil.login(customer.getId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        JSONObject data = new JSONObject();
        data.put("token", tokenInfo.getTokenValue());
        return Result.success(data);
    }

    @GetMapping("/info")
    @Operation(summary = "用户信息")
    public Result<JSONObject> info() {
        Long userId = StpUtil.getLoginIdAsLong();
        CustomerEntity customer = customerService.getById(userId);
        JSONObject data = new JSONObject();
        data.put("userId", customer.getId());
        data.put("userName", customer.getName());
        data.put("userEmail", customer.getUsername());
        return Result.success(data);
    }

    @GetMapping("/summaryData")
    @Operation(summary = "工作台获取客户汇总数据")
    public Result<CustomerStatResp> getSummaryData() {
        CustomerStatResp resp = customerService.getSummaryData(StpUtil.getLoginIdAsLong());
        return Result.success(resp);
    }

    @Operation(summary = "修改密码")
    @PutMapping("/updatePassword")
    public Result<Void> updatePassword(@RequestBody @Validated UserPasswordReq req) {
        customerService.updatePassword(req);
        return Result.success();
    }


    @GetMapping("/logout")
    @Operation(summary = "退出登录")
    public SaResult logout() {
        StpUtil.logout();
        return SaResult.ok();
    }
}
