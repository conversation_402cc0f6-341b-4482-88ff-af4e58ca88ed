package com.lifeng.modules.controller;

import com.lifeng.common.utils.PageParam;
import com.lifeng.common.utils.PageResult;
import com.lifeng.common.utils.Result;
import com.lifeng.modules.model.response.NoticeResp;
import com.lifeng.modules.service.INoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 公告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@RestController
@RequestMapping("/notice")
@Tag(name = "通知公告", description = "通知公告相关接口")
public class NoticeController {
    @Resource
    private INoticeService noticeService;

    @GetMapping("page")
    @Operation(summary = "查询公告",
            description = "分页查询公告列表")
    public Result<PageResult<NoticeResp>> page(@ParameterObject PageParam param) {
        return Result.success(noticeService.pageNotices(param));
    }

}
