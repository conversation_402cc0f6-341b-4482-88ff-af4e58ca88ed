package com.lifeng.modules.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lifeng.common.utils.PageParam;
import com.lifeng.common.utils.PageResult;
import com.lifeng.common.utils.Result;
import com.lifeng.modules.model.entity.RechargeOrderEntity;
import com.lifeng.modules.model.query.OrderQuery;
import com.lifeng.modules.model.response.RechargeOrderResp;
import com.lifeng.modules.service.IRechargeOrderService;
import com.lifeng.modules.service.impl.RechargeOrderServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 充值订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/rechargeOrder")
@RequiredArgsConstructor
@Tag(name = "充值订单管理", description = "充值订单相关接口")
public class RechargeOrderController {

    private final IRechargeOrderService rechargeOrderService;

    @Operation(summary = "分页查询",
            description = "分页查询充值订单")
    @GetMapping("page")
    public Result<PageResult<RechargeOrderResp>> page(@ParameterObject OrderQuery query){
        return Result.success(rechargeOrderService.rechargeOrderPage(query));
    }




}
