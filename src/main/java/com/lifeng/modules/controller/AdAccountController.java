package com.lifeng.modules.controller;

import com.lifeng.common.utils.PageResult;
import com.lifeng.common.utils.Result;
import com.lifeng.modules.model.query.AdAccountListQuery;
import com.lifeng.modules.model.request.*;
import com.lifeng.modules.model.response.AdAccountListInfoResp;
import com.lifeng.modules.service.IAdAccountOrderService;
import com.lifeng.modules.service.IAdAccountService;
import com.lifeng.modules.service.ICustomerOrderGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 广告账号 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@RestController
@RequestMapping("/adAccount")
@RequiredArgsConstructor
@Tag(name = "广告户列表", description = "广告户列表相关接口")
public class AdAccountController {


    private final ICustomerOrderGroupService customerOrderGroupService;

    private final IAdAccountService adAccountService;

    private final IAdAccountOrderService adAccountOrderService;

    @PostMapping("/distributeGroup")
    @Operation(summary = "分配分组")
    public Result<Void> distributeGroup(@RequestBody @Validated DistributeGroupReq req) {
        customerOrderGroupService.distribute(req);
        return Result.success();
    }


    @Operation(summary = "分页查询",
            description = "分页查询广告户列表")
    @GetMapping("/page")
    public Result<PageResult<AdAccountListInfoResp>> page(@ParameterObject AdAccountListQuery query) {
        return Result.success(adAccountService.customPage(query));
    }

    @PostMapping("/clear")
    @Operation(summary = "清零")
    private Result<Void> clear(@RequestBody @Validated AdAccountClearReq req) {
        adAccountOrderService.clear(req);
        return Result.success();
    }

    @PostMapping("/recharge")
    @Operation(summary = "充值")
    private Result<Void> recharge(@RequestBody @Validated AdAccountRechargeReq req) {
        adAccountOrderService.recharge(req);
        return Result.success();
    }

    @PostMapping("/refund")
    @Operation(summary = "减款")
    private Result<Void> refund(@RequestBody @Validated AdAccountRefundReq req) {
        adAccountOrderService.refund(req);
        return Result.success();
    }

    @PostMapping("/receive")
    @Operation(summary = "接收")
    private Result<Void> receive(@RequestBody @Validated AdAccountReceiveReq req) {
        adAccountOrderService.receive(req);
        return Result.success();
    }

}
