package com.lifeng.modules.controller;

import cn.hutool.json.JSONObject;
import com.lifeng.common.utils.Result;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.lifeng.modules.constants.CommonConstants.CAPTCHA_KEY_PREFIX;

@Tag(name = "验证码 API")
@RestController
@RequestMapping("/captcha")
@RequiredArgsConstructor
public class CaptchaController {

    private final StringRedisTemplate redisTemplate;


    @GetMapping("/image")
    @Operation(summary = "获取图片验证码", description = "获取图片验证码")
    public Result<JSONObject> captcha() {
        Captcha captcha = new SpecCaptcha();

        String code = captcha.text().toLowerCase();
        String imageBase64 = captcha.toBase64();

        String key = UUID.randomUUID().toString().replaceAll("-", "");
        String redisKey = CAPTCHA_KEY_PREFIX + key;
        redisTemplate.opsForValue().set(redisKey, code, 2, TimeUnit.MINUTES);

        JSONObject data = new JSONObject();
        data.set("key", key);
        data.set("image", imageBase64);
        return Result.success(data);
    }
}