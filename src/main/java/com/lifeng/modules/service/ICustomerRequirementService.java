package com.lifeng.modules.service;

import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.CustomerRequirementEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lifeng.modules.model.request.AdAccountOrderAddReq;
import com.lifeng.modules.model.request.AdAccountOrderReq;
import com.lifeng.modules.model.response.CustomerRequirementResp;

/**
 * <p>
 * 客户需求 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface ICustomerRequirementService extends IService<CustomerRequirementEntity> {

    /**
     * 自定义分页查询
     *
     * @param req 请求参数
     * @return 分页结果
     */
    PageResult<CustomerRequirementResp> customPage(AdAccountOrderReq req);

    /**
     * 新增客户需求
     *
     * @param req 请求参数
     */
    void add(AdAccountOrderAddReq req);
}
