package com.lifeng.modules.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lifeng.common.utils.PageParam;
import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.RechargeOrderEntity;
import com.lifeng.modules.mapper.RechargeOrderMapper;
import com.lifeng.modules.model.query.OrderQuery;
import com.lifeng.modules.model.response.RechargeOrderResp;
import com.lifeng.modules.service.IRechargeOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 充值订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class RechargeOrderServiceImpl extends ServiceImpl<RechargeOrderMapper, RechargeOrderEntity> implements IRechargeOrderService {

    @Override
    public PageResult<RechargeOrderResp> rechargeOrderPage(OrderQuery pageParam) {
        IPage<RechargeOrderResp> page = this.baseMapper.selectRechargeOrderPage(pageParam.getPage(),pageParam, StpUtil.getLoginIdAsLong());
        return PageResult.newInstance(page);
    }

}
