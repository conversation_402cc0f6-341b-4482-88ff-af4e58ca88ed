package com.lifeng.modules.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.AdAccountEntity;
import com.lifeng.modules.mapper.AdAccountMapper;
import com.lifeng.modules.model.query.AdAccountListQuery;
import com.lifeng.modules.model.response.AdAccountListInfoResp;
import com.lifeng.modules.service.IAdAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告账号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
public class AdAccountServiceImpl extends ServiceImpl<AdAccountMapper, AdAccountEntity> implements IAdAccountService {

    @Override
    public PageResult<AdAccountListInfoResp> customPage(AdAccountListQuery query) {

        IPage<AdAccountListInfoResp> page = this.baseMapper.customPage(query.getPage(),query, StpUtil.getLoginIdAsLong());

        return PageResult.newInstance(page);
    }


}
