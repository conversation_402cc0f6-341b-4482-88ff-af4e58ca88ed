package com.lifeng.modules.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lifeng.common.utils.PageResult;
import com.lifeng.common.utils.Result;
import com.lifeng.modules.model.entity.AdAccountInsightEntity;
import com.lifeng.modules.mapper.AdAccountInsightMapper;
import com.lifeng.modules.model.query.DailyConsumptionStatisticsQuery;
import com.lifeng.modules.model.request.DailyInsightReq;
import com.lifeng.modules.model.response.AdAccountDailyInsightResp;
import com.lifeng.modules.model.response.DailyConsumptionStatisticsRecordResp;
import com.lifeng.modules.service.IAdAccountInsightService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 广告户每日消耗 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
@Slf4j
public class AdAccountInsightServiceImpl extends ServiceImpl<AdAccountInsightMapper, AdAccountInsightEntity> implements IAdAccountInsightService {

    @Override
    public List<DailyConsumptionStatisticsRecordResp> getDailyConsumptionStatistics(DailyConsumptionStatisticsQuery query) {
        List<DailyConsumptionStatisticsRecordResp> dailyConsumptionStatistics = this.baseMapper.getDailyConsumptionStatistics(query, StpUtil.getLoginIdAsLong());
        return dailyConsumptionStatistics;
    }

    @Override
    public PageResult<AdAccountDailyInsightResp> getAdAccountDailyInsight(DailyInsightReq req ) {
        IPage<AdAccountDailyInsightResp> page = this.baseMapper.getAdAccountDailyInsight(req.getPage(), req.getAdAccountId(), StpUtil.getLoginIdAsLong());
        return PageResult.newInstance(page);
    }
}
