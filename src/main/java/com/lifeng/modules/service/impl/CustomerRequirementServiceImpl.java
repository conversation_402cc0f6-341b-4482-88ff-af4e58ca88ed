package com.lifeng.modules.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lifeng.common.exception.CustomException;
import com.lifeng.common.utils.MessageUtils;
import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.mapper.CustomerRequirementMapper;
import com.lifeng.modules.model.entity.CustomerRequirementEntity;
import com.lifeng.modules.model.request.AdAccountOrderAddReq;
import com.lifeng.modules.model.request.AdAccountOrderReq;
import com.lifeng.modules.model.response.CustomerRequirementResp;
import com.lifeng.modules.server.FinanceServerHelper;
import com.lifeng.modules.server.model.ProtocolResponse;
import com.lifeng.modules.service.ICustomerRequirementService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户需求 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
@RequiredArgsConstructor
public class CustomerRequirementServiceImpl extends ServiceImpl<CustomerRequirementMapper, CustomerRequirementEntity> implements ICustomerRequirementService {

    private final FinanceServerHelper financeServerHelper;

    private final MessageUtils messageUtils;


    @Override
    public PageResult<CustomerRequirementResp> customPage(AdAccountOrderReq req) {
        IPage<CustomerRequirementResp> page = baseMapper.customPage(req.getPage(), req, StpUtil.getLoginIdAsLong());
        return PageResult.newInstance(page);
    }

    @Override
    public void add(AdAccountOrderAddReq req) {
        JSONObject data = JSONObject.from(req);
        data.put("customerId", StpUtil.getLoginIdAsLong());
        ProtocolResponse<?> response = financeServerHelper.post("/api/order/requirement/add", data);
        if (response.getCode() != 0) {
            throw new CustomException(messageUtils.getMessage("system.error"));
        }
    }
}
