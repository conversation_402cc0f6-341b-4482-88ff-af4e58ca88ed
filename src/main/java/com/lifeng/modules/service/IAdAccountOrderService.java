package com.lifeng.modules.service;

import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.AdAccountOrderEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lifeng.modules.model.request.*;
import com.lifeng.modules.model.response.CustomerRequirementResp;

/**
 * <p>
 * 下户订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface IAdAccountOrderService extends IService<AdAccountOrderEntity> {


    /**
     * 清零订单
     *
     * @param req
     */
    void clear(AdAccountClearReq req);

    /**
     * 充值订单
     *
     * @param req
     */
    void recharge(AdAccountRechargeReq req);

    /**
     * 减款订单
     *
     * @param req
     */
    void refund(AdAccountRefundReq req);

    /**
     * 接收广告户
     *
     * @param req
     */
    void receive(AdAccountReceiveReq req);
}
