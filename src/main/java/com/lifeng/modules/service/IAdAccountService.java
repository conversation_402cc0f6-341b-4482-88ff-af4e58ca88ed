package com.lifeng.modules.service;

import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.AdAccountEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lifeng.modules.model.query.AdAccountListQuery;
import com.lifeng.modules.model.response.AdAccountListInfoResp;

/**
 * <p>
 * 广告账号 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface IAdAccountService extends IService<AdAccountEntity> {

    PageResult<AdAccountListInfoResp> customPage(AdAccountListQuery query);
}
