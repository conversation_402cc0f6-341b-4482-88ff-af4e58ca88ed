package com.lifeng.modules.service;

import com.lifeng.common.utils.PageResult;
import com.lifeng.modules.model.entity.CustomerOrderGroupEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lifeng.modules.model.query.GroupQuery;
import com.lifeng.modules.model.request.AdAccountOrderReq;
import com.lifeng.modules.model.request.DistributeGroupReq;
import com.lifeng.modules.model.request.GroupReq;
import com.lifeng.modules.model.response.GroupResp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户下户订单分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface ICustomerOrderGroupService extends IService<CustomerOrderGroupEntity> {

    /**
     * 自定义分页查询
     *
     * @param req 请求参数
     * @return 分页结果
     */
    PageResult<GroupResp> customPage(GroupQuery req);

    /**
     * 新增分组
     *
     * @param req
     */
    void add(GroupReq req);

    /**
     * 编辑分组
     *
     * @param req
     */
    void edit(GroupReq req);

    /**
     * 删除分组
     *
     * @param ids
     */
    void delete(List<Long> ids);


    /**
     * 分配广告户
     *
     * @param req 广告户ID列表
     */
    void distribute(DistributeGroupReq req);

    /**
     * 分组列表
     *
     * @return
     */
    List<Map<String, Object>>listByCustomer();
}
