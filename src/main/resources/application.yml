# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  port: 8080
  servlet:
    context-path: /ws
    session:
      cookie:
        http-only: true

spring:
  # 环境 dev|test|prod|prodd
  application:
    name: whatsapp-cms
  profiles:
    active: prodd
  messages:
    encoding: UTF-8
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB
      enabled: true

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.whatsapp.cms.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型
      id-type: AUTO
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true

socketio:
  host: 127.0.0.1
  port: 9092
  # socket连接数大小（如只监听一个端口boss线程组为1即可）
  bossCount: 1
  workCount: 100
