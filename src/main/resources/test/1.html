<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>1</title>
    <base>
    <script src="https://cdn.bootcss.com/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.bootcss.com/socket.io/4.7.4/socket.io.js"></script>
    <style>
        body {
            padding: 20px;
        }

        #console {
            height: 450px;
            overflow: auto;
        }

        .username-msg {
            color: orange;
        }

        .connect-msg {
            color: green;
        }

        .disconnect-msg {
            color: red;
        }
    </style>
</head>

<body>
<div id="console" class="well"></div>
<div id="input">
    <input type="text" id="message" placeholder="请输入消息"/>
    <button id="send" onclick="sendMessage()">发送</button>
</div>
</body>
<script type="text/javascript">
    var socket;
    connect();

    function connect() {
        var opts = {
            query: 'accountId=2&groupId=44',
            transports: ['websocket']
        };
        socket = io.connect('http://127.0.0.1:9092/wab', opts);
        socket.on('connect', function () {
            console.log("连接成功");
            serverOutput('<span class="connect-msg">连接成功</span>');
            console.log('socket.id', socket.id);
        });
        socket.on('message', function (data) {
            output('<span class="username-msg">' + JSON.stringify(data) + ' </span>');
            console.log(data);
        });

        socket.on('disconnect', function () {
            serverOutput('<span class="disconnect-msg">' + '已下线! </span>');
        });

        socket.on('broadcast', function (data) {
            output('<span class="username-msg">' + JSON.stringify(data) + ' </span>');
            console.log('broadcast', data);
        })
    }

    function output(message) {
        var element = $("<div>" + " " + message + "</div>");
        $('#console').prepend(element);
    }

    function serverOutput(message) {
        var element = $("<div>" + message + "</div>");
        $('#console').prepend(element);
    }

    function sendMessage() {
        // socket.send("test", JSON.stringify({ name: "John" }));
        socket.emit("broadcast", JSON.stringify({name: "John"}), function (data) {
            console.log('ack', data);
        });
    }
</script>
</html>