# Common validation messages
validation.notNull=Parameter cannot be null
validation.notBlank=Parameter cannot be blank
validation.notEmpty=Parameter cannot be empty
validation.size.min=Length cannot be less than {min} characters
validation.size.max=Length cannot exceed {max} characters
validation.size.range=Length must be between {min}-{max} characters
validation.email=Invalid email format
validation.phone=Invalid phone number format
validation.positive=Must be a positive number
validation.min=Cannot be less than {value}
validation.max=Cannot be greater than {value}

# Login related
login.username.notBlank=Username cannot be blank
login.username.size=Username length must be between {min}-{max} characters
login.password.notBlank=Password cannot be blank
login.password.size=Password length must be between {min}-{max} characters
login.failed=Invalid username or password
login.success=Login successful
login.logout.success=Logout successful
login.captcha.error=Invalid captcha
login.captcha.notBlank=Captcha cannot be blank
login.captcha.invalid=Captcha is invalid
login.password.not.match = Old password does not match the current password
login.password.mismatch = New password does not match the confirmation password

# Login lockout related
login.account.locked=Account has been locked, please try again in {0} minutes
login.password.error.locked=Too many password errors, account locked for {0} hours
login.error = Wrong username or password
login.termination=Account has been terminated

# File upload related
file.upload.success=File uploaded successfully
file.upload.failed=File upload failed
file.format.invalid=Invalid file format
file.size.exceed=File size exceeds limit
file.not.found=File not found

# System related
system.error=System error, please contact administrator
system.param.invalid=Invalid request parameters
system.method.not.supported=Request method not supported
system.param.missing=Missing request parameter: {0}
system.param.type.mismatch=Invalid request parameter type: {0}
system.url.not.found={0}
system.access.denied=Access denied
system.unauthorized=Unauthorized access

# Customer related
customer.name.notBlank=Customer name cannot be blank
customer.name.size=Customer name length must be between {min}-{max} characters
customer.username.notBlank=Username cannot be blank
customer.password.notBlank=Password cannot be blank
customer.email.format=Invalid email format
customer.phone.format=Invalid phone number format
customer.feeRate.range=Fee rate percentage must be between 0.0-1.0
customer.balance.positive=Balance must be positive
customer.create.success=Customer created successfully
customer.update.success=Customer updated successfully
customer.delete.success=Customer deleted successfully
customer.not.found=Customer not found

# Ad Account Order related
adAccount.name.notBlank=Ad account name cannot be blank
adAccount.name.size=Ad account name length must be between {min}-{max} characters
adAccount.customerBmId.notBlank=Customer BM ID cannot be blank
adAccount.customerEmail.notBlank=Customer email cannot be blank
adAccount.customerEmail.format=Invalid customer email format
adAccount.timezone.notBlank=Timezone cannot be blank
adAccount.quantity.notNull=Purchase quantity cannot be null
adAccount.quantity.min=Purchase quantity cannot be less than {value}
adAccount.not.exists=Ad account does not exist

# Ad Account Recharge and Refund related
adAccount.recharge.amount.notNull=Recharge amount cannot be null
adAccount.recharge.amount.positive=Recharge amount must be greater than 0
adAccount.refund.amount.notNull=Refund amount cannot be null
adAccount.refund.amount.positive=Refund amount must be greater than 0
adAccount.id.notNull=Ad account ID cannot be null

# System related
system.welcome=Welcome to the system
# Business related
business.operation.success=Operation successful
business.operation.failed=Operation failed
business.data.not.found=Data not found
business.data.exists=Data already exists
business.permission.denied=Permission denied
business.status.invalid=Invalid status
business.query.success=Query successful

# Pagination related
page.param.invalid=Invalid pagination parameters
page.size.exceed=Page size exceeds limit
page.number.invalid=Invalid page number

# Result class related
result.success=Operation successful
result.failure=System error, please contact administrator

# Group correlation
group.null=Group does not exist
group.exists=Group already exists
group.name.exists=Group name already exists