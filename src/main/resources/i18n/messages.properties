# 通用校验消息
validation.notNull=参数不能为空
validation.notBlank=参数不能为空
validation.notEmpty=参数不能为空
validation.size.min=长度不能少于{min}个字符
validation.size.max=长度不能超过{max}个字符
validation.size.range=长度必须在{min}-{max}个字符之间
validation.email=邮箱格式不正确
validation.phone=手机号格式不正确
validation.positive=必须是正数
validation.min=不能小于{value}
validation.max=不能大于{value}

# 登录相关
login.username.notBlank=用户名不能为空
login.username.size=用户名长度必须在{min}-{max}个字符之间
login.password.notBlank=密码不能为空
login.password.size=密码长度必须在{min}-{max}个字符之间
login.failed=用户名或密码错误
login.success=登录成功
login.logout.success=退出登录成功
login.captcha.error=验证码错误
login.captcha.notBlank=验证码不能为空
login.captcha.invalid=验证码无效
login.password.mismatch = 两次输入的密码不一致
login.password.not.match = 旧密码与当前密码不匹配

# 登录锁定相关
login.account.locked=账号已被锁定，请在 {0} 分钟后重试
login.password.error.locked=密码错误次数过多，账号已锁定{0}小时
login.error=用户名或密码错误
login.termination=用户已终止

# 文件上传相关
file.upload.success=文件上传成功
file.upload.failed=文件上传失败
file.format.invalid=文件格式不正确
file.size.exceed=文件大小超出限制
file.not.found=文件不存在

# 系统相关
system.error=系统异常，请联系管理员
system.param.invalid=请求参数不正确
system.method.not.supported=请求方法不正确
system.param.missing=请求参数缺少: {0}
system.param.type.mismatch=请求参数类型不正确：{0}
system.url.not.found={0}
system.access.denied=访问被拒绝
system.unauthorized=未授权访问

# 客户相关
customer.name.notBlank=客户名称不能为空
customer.name.size=客户名称长度必须在{min}-{max}个字符之间
customer.username.notBlank=用户名不能为空
customer.password.notBlank=密码不能为空
customer.email.format=邮箱格式不正确
customer.phone.format=手机号格式不正确
customer.feeRate.range=手续费百分比必须在0.0-1.0之间
customer.balance.positive=余额必须为正数
customer.create.success=客户创建成功
customer.update.success=客户更新成功
customer.delete.success=客户删除成功
customer.not.found=客户不存在

# 广告账户订单相关
adAccount.name.notBlank=广告账户名称不能为空
adAccount.name.size=广告账户名称长度必须在{min}-{max}个字符之间
adAccount.customerBmId.notBlank=客户BM ID不能为空
adAccount.customerEmail.notBlank=客户邮箱不能为空
adAccount.customerEmail.format=客户邮箱格式不正确
adAccount.timezone.notBlank=时区不能为空
adAccount.quantity.notNull=购买数量不能为空
adAccount.quantity.min=购买数量不能小于{value}
adAccount.not.exists=广告户不存在

# 广告账户充值退款相关
adAccount.recharge.amount.notNull=充值金额不能为空
adAccount.recharge.amount.positive=充值金额必须大于0
adAccount.refund.amount.notNull=退款金额不能为空
adAccount.refund.amount.positive=退款金额必须大于0
adAccount.id.notNull=广告户ID不能为空

# 系统相关
system.welcome=欢迎使用系统

# 业务相关
business.operation.success=操作成功
business.operation.failed=操作失败
business.data.not.found=数据不存在
business.data.exists=数据已存在
business.permission.denied=权限不足
business.status.invalid=状态不正确
business.query.success=查询成功

# 分页相关
page.param.invalid=分页参数不正确
page.size.exceed=每页大小超出限制
page.number.invalid=页码不正确

# Result类相关
result.success=操作成功
result.failure=系统异常，请联系管理员

# 分组相关
group.null=分组不存在
group.exists=分组已存在
group.name.exists=分组名称已存在