spring:
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************************************************************************************************
      username: root
      password: _y!aRBt(%9nG
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  boot:
    admin:
      client:
        url: http://localhost:8081

  servlet:
    multipart:
      location: /opt/ws/tmp

logging:
  file:
    name: /opt/ws/logs/all_ws-logback.log
  level:
    root: info

file:
  upload:
    path: /home/<USER>/img/
    host: https://wabapi.kmnd.site/
    download: http://wabapi.kmnd.site/

lamax: 10