<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lifeng.modules.mapper.CustomerMapper">

    <select id="selectSummaryData" resultType="com.lifeng.modules.model.response.CustomerStatResp">
        SELECT
            COUNT(*) AS totalAccount,

            SUM(CASE WHEN account_status = 2 THEN 1 ELSE 0 END) AS deadAccount,
            SUM(CASE WHEN account_status = 1 THEN 1 ELSE 0 END) AS normalAdCount,
            COALESCE(SUM(total_spent), 0) AS totalSpend,
            COALESCE(SUM(recharge_amount), 0) AS totalRecharge,
            COALESCE(
                SUM(CASE
                    WHEN NOT enable_prepay AND clear_status != 3 AND status = 3
                    THEN (spend_cap - amount_spent)
                    ELSE 0
                END) +
                SUM(CASE
                    WHEN enable_prepay AND clear_status != 3 AND status = 3
                    THEN (recharge_amount - order_total_spent)
                    ELSE 0
                END), 0
            ) AS accountBalance,
            MAX(balance) AS balance,
            ROUND(
                CASE
                    WHEN COUNT(*) > 0
                    THEN SUM(CASE WHEN account_status = 2 THEN 1 ELSE 0 END) / COUNT(*) * 100
                    ELSE 0
                END, 2
            ) AS deadRate
        FROM (
            SELECT
                o.customer_id,
                c.balance,
                o.enable_prepay,
                o.ad_account_id,
                a.account_status,
                a.clear_status,
                o.status,
                a.spend_cap,
                a.amount_spent,
                o.total_spent as order_total_spent,
                -- 充值金额计算
                COALESCE(
                    (
                    SELECT
                        COALESCE(SUM(CASE WHEN r.type = 3 THEN r.amount ELSE 0 END), 0) -
                        COALESCE(SUM(CASE WHEN r.type IN (4, 5) THEN r.amount ELSE 0 END), 0)
                    FROM biz_customer_balance_record r
                    WHERE r.platform_ad_id = o.ad_account_id
                        AND r.customer_id = o.customer_id
                        AND r.trans_time >= o.pay_time
                        AND (o.status != 5 OR r.trans_time &lt;= o.recycle_time)
                        ), 0
                ) AS recharge_amount,
                -- 总消耗计算
                COALESCE(
                    (
                        SELECT SUM(i.spend)
                        FROM biz_ad_account_insight i
                        WHERE i.customer_id = o.customer_id
                        AND i.ad_account_id = o.ad_account_id
                        AND i.stat_date >= o.finish_time
                        AND (o.status != 5 OR i.stat_date &lt;= o.recycle_time)
                    ), 0
                ) AS total_spent
            FROM biz_ad_account_order o
            LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
            LEFT JOIN biz_customer c ON c.id = o.customer_id
            WHERE o.status IN (3, 5) AND o.customer_id = #{customerId}
        ) customer_order_stat
    </select>

    <select id="selectIdleAccount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT CASE WHEN COALESCE(card_spent, 0) &lt; 10 THEN o.ad_account_id END)
        FROM biz_ad_account_order o
        LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
        LEFT JOIN (
            SELECT
            ad_account_id,
            COALESCE(-SUM(trans_amount), 0) AS card_spent
            FROM
            biz_card_transaction
            WHERE
            trans_status != 4
            AND stat_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            AND customer_id = #{customerId}
            GROUP BY
            ad_account_id
        ) ct ON ct.ad_account_id = o.ad_account_id
        WHERE o.status = 3 AND a.account_status = 1
            AND o.finish_time &lt; DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            AND o.customer_id = #{customerId}
    </select>
</mapper>
