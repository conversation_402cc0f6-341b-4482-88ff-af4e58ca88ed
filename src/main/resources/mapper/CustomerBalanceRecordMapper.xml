<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lifeng.modules.mapper.CustomerBalanceRecordMapper">


    <select id="selectCustomerBalanceRecordPage"
            resultType="com.lifeng.modules.model.response.AdAccountBalanceResp">
        select bcbr.platform_ad_id as adAccountId, bcbr.type,bcbr.action,bcbr.amount,
         bcbr.after_amount, bcbr.trans_time
        from biz_customer_balance_record bcbr
        <where>
                and bcbr.customer_id =  #{customerId}
            <if test="query.adAccountId != null">
                and bcbr.platform_ad_id = #{query.adAccountId}
            </if>
            <if test="query.action != null">
                and bcbr.action = #{query.action}
            </if>
            <if test="query.type != null">
                and bcbr.type = #{query.type}
            </if>
            <if test="query.beginTime != null and query.endTime != null">
                and bcbr.trans_time between #{query.beginTime} and #{query.endTime}
            </if>
        </where>
        order by bcbr.trans_time desc,id desc
    </select>
</mapper>
