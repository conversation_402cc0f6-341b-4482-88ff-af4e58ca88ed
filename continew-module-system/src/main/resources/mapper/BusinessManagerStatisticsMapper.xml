<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.BusinessManagerStatisticsMapper">

    <select id="selectSpendSummary" resultType="top.continew.admin.biz.model.resp.SpendStatisticsSummaryResp">
        SELECT COUNT(*)                                                        as totalOrderCount,
               IFNULL(SUM(IF(a.account_status = 1 and o.status = 3, 1, 0)), 0) as totalNormalCount,
               IFNULL(SUM(IF(o.status = 5, 1, 0)), 0)                          as recycleOrderCount,
               IFNULL(SUM(IF(o.status = 3, 1, 0)), 0)                          as saleOrderCount,
               IFNULL(SUM(o.cost), 0)                                          as totalOrderCost
        FROM biz_ad_account_order o
                 LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
                 LEFT JOIN biz_customer c on c.id = o.customer_id
        WHERE o.status in (3, 5)
          AND o.finish_time BETWEEN #{req.completeTimeStart} AND #{req.completeTimeEnd}
        AND c.is_self_account = false
    </select>

    <select id="selectDailySpend" resultType="top.continew.admin.biz.model.resp.DailySpendResp">
        SELECT trans_date                                  as tradeTime,
               SUM(daily_spend) OVER (ORDER BY trans_date) as totalSpend
        FROM (SELECT DATE(t.stat_time)  as trans_date,
                     COALESCE(-SUM(t.trans_amount), 0) as daily_spend
              FROM biz_card_transaction t
                       inner join (select o.ad_account_id, o.status, o.finish_time, o.recycle_time
                                   from biz_ad_account_order o
                                            left join biz_customer c on c.id = o.customer_id
                                   where c.is_self_account = false
                                     and o.status in (3, 5)
                                     and o.finish_time between #{req.completeTimeStart} and #{req.completeTimeEnd}) o
                                  on t.ad_account_id = o.ad_account_id
              WHERE t.stat_time between GREATEST(#{req.statisticsTimeStart}
                  , o.finish_time)
                  and LEAST(#{req.statisticsTimeEnd}
                      , ifnull(o.recycle_time
                                , #{req.statisticsTimeEnd}))
                AND t.trans_status != 4
              GROUP BY DATE(t.stat_time)) daily_stats
        ORDER BY trans_date
                desc;
    </select>
    <select id="getSummary"
            resultType="top.continew.admin.biz.model.resp.BusinessManagerStatisticsSummaryResp">
        select COALESCE(SUM(daily_received_count), 0) as receiveBMItem,
        COALESCE(SUM(total_cost), 0) as receiveCost,
        COALESCE(SUM(used_normal), 0) as usedNormalBmItem,
        COALESCE(ROUND(SUM(used_normal) / SUM(daily_received_count) * 100, 2), 0) as normalRate,
        COALESCE(SUM(purchase_cost), 0) as purchaseCost,
        COALESCE(ROUND(SUM(purchase_cost) / COUNT(*), 2), 0) as avgPurchaseCostForDay,
        COALESCE(ROUND(SUM(total_cost) / SUM(used_normal), 2), 0) as avgPrepareCost
        from biz_business_manager_statistics
        <if test="query.statisticsDate != null and query.statisticsDate.length > 0">
            where create_time between #{query.statisticsDate[0]} and #{query.statisticsDate[1]}
        </if>
    </select>

    <select id="listStatChannelData"
            resultType="top.continew.admin.biz.model.resp.BusinessManagerChannelStatDataResp">
        select
        channel_id AS channelId,
        channel_name AS channelName,
        COUNT(*) AS totalCount,
        SUM(CASE WHEN status = 1 AND is_use = 0 THEN 1 ELSE 0 END) AS availableCount,
        SUM(CASE WHEN status = 2 AND is_use = 1 THEN 1 ELSE 0 END) AS usedBannedCount,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS bannedCount,
        SUM(CASE WHEN limit_count > 0 THEN 1 ELSE 0 END) AS lowerLimitCount,
        SUM(limit_count) AS lowerLimitAdCount
        from (select bm.platform_id,
            bm.status,
            bm.is_use,
            bm.channel_id,
            bmc.name as channel_name,
            date(bm.create_time) as stat_date,
            (select count(*)
            from biz_ad_account ad
            where ad.business_manager_id = bm.id
            and real_adtrust_dsl = 50) as limit_count
            from biz_business_manager bm
            left join biz_business_manager_channel bmc on bmc.id = bm.channel_id
            <where>
            <if test="null!= query.statisticsDate and query.statisticsDate.length==2">
                AND bm.create_time BETWEEN #{query.statisticsDate[0]} AND #{query.statisticsDate[1]}
            </if>
            <if test="null != query.channelId">
                and bm.channel_id = #{query.channelId}
            </if>
            <if test="null != query.bmType">
                AND bm.type = #{query.bmType}
            </if>
            </where>
            ) data
        GROUP BY channelId
        ORDER BY channelName DESC
    </select>


    <select id="listStatChannelAdData" resultType="java.util.Map">
        SELECT
        bm.channel_id AS channelId,
        COUNT(*) AS adAccountTotalCount,
        SUM(CASE WHEN ad.sale_status = 5 THEN 1 ELSE 0 END) AS adAccountInvalidCount
        FROM  biz_ad_account ad
        LEFT JOIN biz_business_manager bm ON bm.id = ad.business_manager_id
        WHERE bm.channel_id IN
        <foreach collection="channelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="null != bmType">
            AND bm.type = #{bmType}
        </if>
        GROUP BY bm.channel_id
    </select>

    <select id="listStatChannelAdOrderData" resultType="java.util.Map">
        SELECT
        bm.channel_id AS channelId,
        COALESCE(COUNT(o.id), 0) AS adAccountOrderCount,
        SUM(CASE
                WHEN o.status IN (3, 5) THEN
                    CASE
                        WHEN (
                            SELECT -SUM(t.trans_amount)
                            FROM biz_card_transaction t
                            WHERE t.ad_account_id = o.ad_account_id and t.customer_id = o.customer_id
                            AND t.trans_status != 4
                        ) <![CDATA[<]]>  10 THEN 1 ELSE 0
                    END
                ELSE 1
            END) AS adAccountOrderNoSpendCount
        FROM biz_ad_account_order o
        LEFT JOIN biz_ad_account ad  ON o.ad_account_id = ad.platform_ad_id
        LEFT JOIN biz_business_manager bm ON bm.id = ad.business_manager_id
        WHERE bm.channel_id IN
        <foreach collection="channelIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="null != bmType">
            AND bm.type = #{bmType}
        </if>
        GROUP BY bm.channel_id
    </select>

    <select id="listStatBanData"
            resultType="top.continew.admin.biz.model.resp.BusinessManagerBanStatDataResp">
        select
            <if test="query.groupByChannel">
                bmc.name as channelName,
            </if>
            bm.banned_reason as bannedReason,
            count(*) as bannedCount
        from biz_business_manager bm
            left join biz_business_manager_channel bmc on bmc.id = bm.channel_id
        where bm.status = 2
            <if test="null != query.statisticsDate">
                and bm.create_time between #{query.statisticsDate[0]} and #{query.statisticsDate[1]}
            </if>
            <if test="null != query.channelId">
                and bm.channel_id = #{query.channelId}
            </if>
        group by
        <if test="query.groupByChannel">
            bmc.name,
        </if>
            bm.banned_reason having bannedCount > 0
    </select>

</mapper>