<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.crm.OpportunityMapper">

    <update id="updateLongTermFollowUp">
        update biz_opportunity set status=3,remind_time=#{remindTime} where status=2
    </update>

    <select id="selectOpportunityPage" resultType="top.continew.admin.biz.model.resp.crm.OpportunityResp">
        SELECT o.*
        FROM biz_opportunity o
        LEFT JOIN sys_user ur ON o.handler_user_id = ur.id
        <where>
            <if test="query.customerId != null">
                AND o.customer_id = #{query.customerId}
            </if>
            <if test="query.sourceId != null">
                AND o.source_id = #{query.sourceId}
            </if>
            <if test="query.status != null">
                AND o.status = #{query.status}
            </if>
            <if test="query.requirement != null and query.requirement != ''">
                AND o.requirement LIKE CONCAT('%', #{query.requirement}, '%')
            </if>
            <if test="query.lostReason != null">
                AND o.lost_reason = #{query.lostReason}
            </if>
            <if test="query.handlerUserId != null">
                AND o.handler_user_id = #{query.handlerUserId}
            </if>
            <if test="query.createUser != null">
                AND o.create_user = #{query.createUser}
            </if>
        </where>
        ORDER BY o.id DESC
    </select>

    <!-- 统计待跟进商机数量 -->
    <select id="countPendingOpportunities" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_opportunity
        WHERE handler_user_id = #{handlerUserId}
        AND status = 1
    </select>

    <!-- 优化后的今日需跟进商机数量统计 -->
    <select id="countTodayFollowOpportunities" resultType="java.lang.Long">
        SELECT 
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 1
                 AND DATE_ADD(create_time, INTERVAL #{waitOverHour} HOUR) BETWEEN #{todayStart} AND #{todayEnd}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 2
                 AND last_follow_time IS NOT NULL
                 AND DATE_ADD(last_follow_time, INTERVAL #{pendingOverHour} HOUR) BETWEEN #{todayStart} AND #{todayEnd}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND remind_time IS NOT NULL
                 AND remind_time BETWEEN #{todayStart} AND #{todayEnd}), 0
            ) AS total_count
    </select>

    <!-- 优化后的超时商机数量统计 -->
    <select id="countOverdueOpportunities" resultType="java.lang.Long">
        SELECT 
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 1
                 AND DATE_ADD(create_time, INTERVAL #{waitOverHour} HOUR) &lt; #{currentTime}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND status = 2
                 AND last_follow_time IS NOT NULL
                 AND DATE_ADD(last_follow_time, INTERVAL #{pendingOverHour} HOUR) &lt; #{currentTime}), 0
            ) +
            COALESCE(
                (SELECT COUNT(*) FROM biz_opportunity 
                 WHERE handler_user_id = #{handlerUserId}
                 AND remind_time IS NOT NULL
                 AND remind_time &lt; #{currentTime}), 0
            ) AS total_count
    </select>
</mapper>