package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.PurchaseOrderStatusEnum;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 采购订单信息
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "采购订单信息")
public class PurchaseOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    private Long channelId;

    private String channelName;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    private PurchaseOrderTypeEnum type;

    /**
     * 预计采购数量
     */
    @Schema(description = "预计采购数量")
    private Integer expectNum;

    /**
     * 预计采购金额
     */
    @Schema(description = "预计采购金额")
    private BigDecimal totalPrice;

    /**
     * 是否付款
     */
    @Schema(description = "是否付款")
    private Boolean isPay;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    @Schema(description = "付款金额")
    private BigDecimal payPrice;

    /**
     * 状态（1=进行中，2=已完成）
     */
    @Schema(description = "状态（1=进行中，2=已完成）")
    private PurchaseOrderStatusEnum status;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private Integer receiveNum;

    private BigDecimal receivePrice;

    private String receiveUser;

    private Integer writeNum;

    private BigDecimal writePrice;

    private LocalDate purchaseTime;
}