package top.continew.admin.biz.service.impl.crm;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.LeadStatusEnum;
import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.mapper.crm.LeadMapper;
import top.continew.admin.biz.mapper.crm.LeadFollowMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.entity.crm.LeadFollowDO;
import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.admin.biz.model.query.crm.LeadFollowQuery;
import top.continew.admin.biz.model.req.CustomerReq;
import top.continew.admin.biz.model.req.crm.*;
import top.continew.admin.biz.model.resp.crm.CustomerAccountResp;
import top.continew.admin.biz.model.resp.crm.LeadDetailResp;
import top.continew.admin.biz.model.resp.crm.LeadResp;
import top.continew.admin.biz.model.resp.crm.LeadFollowResp;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.crm.CustomerAccountService;
import top.continew.admin.biz.service.crm.LeadService;
import top.continew.admin.biz.service.crm.OpportunityService;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.core.validation.ValidationUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 线索业务实现
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Service
@RequiredArgsConstructor
public class LeadServiceImpl extends BaseServiceImpl<LeadMapper, LeadDO, LeadResp, LeadDetailResp, LeadQuery, LeadReq> implements LeadService {
    private final LeadFollowMapper leadFollowMapper;
    private final UserService userService;
    private final CustomerAccountService customerAccountService;
    private final CustomerService customerService;
    private final OpportunityService opportunityService;


    @Override
    public PageResp<LeadResp> page(LeadQuery query, PageQuery pageQuery) {
        // 创建分页对象
        Page<LeadDO> page = new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize());
        
        // 调用自定义的分页查询方法
        IPage<LeadDO> leadPage = baseMapper.selectLeadPage(page, query);
        
        // 转换为响应对象
        PageResp<LeadResp> pageResp = PageResp.build(leadPage, LeadResp.class);
        
        // 填充最后跟进信息
        pageResp.getList().forEach(lead -> {
            if (!LeadStatusEnum.TO_FOLLOW_UP.equals(lead.getStatus())) {
                LeadFollowDO lastFollow = leadFollowMapper.selectOne(new LambdaQueryWrapper<LeadFollowDO>()
                        .eq(LeadFollowDO::getLeadId, lead.getId())
                        .orderByDesc(LeadFollowDO::getFollowTime)
                        .last("limit 1"));
                LeadFollowResp lastFollowResp = new LeadFollowResp();
                BeanUtil.copyProperties(lastFollow, lastFollowResp);
                lead.setLastFollow(lastFollowResp);
            }

            this.fill(lead);

        });
        
        return pageResp;
    }

    @Override
    public LeadDetailResp get(Long id) {
        LeadDetailResp detail = super.get(id);
        if (detail != null) {
            detail.setAccounts(customerAccountService.listCustomerAccounts(CustomerAccountService.EntityType.LEAD.getValue(), id));
        }
        return detail;
    }

    @Override
    public void updateLongTermFollowUp() {
        //将当前日期+7天
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime remindTime = now.plusDays(7);
        baseMapper.updateLongTermFollowUp(remindTime);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFollow(LeadFollowReq req) {
        // 1. 校验线索是否存在
        LeadDO leadDO = baseMapper.selectById(req.getLeadId());
        ValidationUtils.throwIf(leadDO == null, "线索不存在");

        req.setContent(null == req.getContent() ? StrUtil.EMPTY : req.getContent());

        // 2. 判断状态是否有发生变化
        boolean statusChanged = req.getStatus() != null && !req.getStatus().equals(leadDO.getStatus());
        
        // 3. 根据状态变化情况处理
        if (!statusChanged) {
            // 状态没有变化，添加跟进记录
            handleNoStatusChange(req);
        } else {
            // 状态发生变化
            handleStatusChange(req, leadDO);
        }
        
        // 4. 添加跟进记录
        addFollowRecord(req);
    }
    
    /**
     * 处理状态没有变化的情况
     */
    private void handleNoStatusChange(LeadFollowReq req) {
        LambdaUpdateWrapper<LeadDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LeadDO::getId, req.getLeadId())
                .set(LeadDO::getLastFollowTime, req.getFollowTime());

        baseMapper.update(null, updateWrapper);
    }



    
    /**
     * 处理状态发生变化的情况
     */
    private void handleStatusChange(LeadFollowReq req, LeadDO leadDO) {
        // 不能从创建商机状态回退到其他状态
        ValidationUtils.throwIf(
            LeadStatusEnum.CREATE_OPPORTUNITY.equals(leadDO.getStatus()) && 
            !LeadStatusEnum.CREATE_OPPORTUNITY.equals(req.getStatus()),
            "已创建商机的线索不能回退到其他状态"
        );
        
        // 根据目标状态处理
        switch (req.getStatus()) {
            case CREATE_OPPORTUNITY:
                handleCreateOpportunityStatus(req, leadDO);
                break;
            case INVALID:
                handleInvalidStatus(req);
                break;
            case LONG_TERM_FOLLOW_UP:
                handleLongTermFollowStatus(req);
                break;
            case TO_FOLLOW_UP:
                handleTodoFollowStatus(req);
                break;
            case FOLLOWING_UP:
                handleFollowingStatus(req);
                break;
            default:
                throw new BusinessException("状态非法");
        }
    }
    
    /**
     * 处理状态变为无效的情况
     */
    private void handleInvalidStatus(LeadFollowReq req) {
        // 无效状态必须提供失效原因
        ValidationUtils.throwIf(req.getInvalidReason() == null, "无效状态必须提供失效原因");

        // 更新线索状态和失效原因
        updateStatusChange(req, req.getInvalidReason(), null, 0L);

    }
    
    /**
     * 处理状态变为长期跟进的情况
     */
    private void handleLongTermFollowStatus(LeadFollowReq req) {
        // 长期跟进状态必须提供提醒时间
        ValidationUtils.throwIf(req.getRemindTime() == null, "长期跟进状态必须提供提醒时间");

        // 更新线索状态和提醒时间
        updateStatusChange(req, 0, req.getRemindTime(), 0L);
    }
    
    /**
     * 处理状态变为创建商机的情况
     */
    private void handleCreateOpportunityStatus(LeadFollowReq req, LeadDO lead) {
        // 需要检查telegramChatId和businessUserId不能为空
        // 创建潜在客户和商机
        // 为线索绑定商机ID
        // 为客户和商机绑定客户账号

        // 这里需要根据实际业务逻辑实现
        ValidationUtils.throwIf(req.getTelegramChatId() == null, "Telegram群ID不能为空");
        ValidationUtils.throwIf(req.getBusinessUserId() == null, "商务用户ID不能为空");


        // 创建潜在客户
        CustomerReq customer = new CustomerReq();
        customer.setName(lead.getCustomerName());
        customer.setIndustry(lead.getCustomerIndustry());
        customer.setBusinessUserId(req.getBusinessUserId());
        customer.setTelegramChatId(req.getTelegramChatId());
        customer.setType(CustomerTypeEnum.POTENTIAL);
        customer.setSourceId(lead.getSourceId());
        customer.setCompanyName(lead.getCompanyName());
        customer.setCustomerPosition(lead.getCustomerPosition());
        customer.setCity(lead.getCity());
        customer.setTeamSize(lead.getTeamSize());
        customer.setDailyTeamSpending(lead.getDailyTeamSpending());
        customer.setProductName(lead.getProductName());
        Long customerId = customerService.add(customer);

        // 创建商机
        OpportunityReq opportunity = new OpportunityReq();
        opportunity.setCustomerId(customerId);
        opportunity.setSourceId(lead.getSourceId());
        opportunity.setStatus(OpportunityStatusEnum.FOLLOWING_UP);
        opportunity.setRequirement(lead.getRequirement());
        opportunity.setHandlerUserId(req.getBusinessUserId());
        opportunity.setCompanyName(lead.getCompanyName());
        opportunity.setCustomerPosition(lead.getCustomerPosition());
        opportunity.setCity(lead.getCity());
        opportunity.setTeamSize(lead.getTeamSize());
        opportunity.setDailyTeamSpending(lead.getDailyTeamSpending());
        opportunity.setProductName(lead.getProductName());
        Long opportunityId = opportunityService.add(opportunity);

        //为客户和商机关联上客户的账号
        List<CustomerAccountResp> leadCustomerAccounts = customerAccountService.listCustomerAccounts(CustomerAccountService.EntityType.LEAD.getValue(), lead.getId());
        if(CollUtil.isNotEmpty(leadCustomerAccounts)) {
            leadCustomerAccounts.forEach(leadCustomerAccount -> {
                CustomerAccountRelDO customerRel = new CustomerAccountRelDO();
                customerRel.setCustomerAccountId(leadCustomerAccount.getAccountId());
                customerRel.setEntityType(CustomerAccountService.EntityType.CUSTOMER.getValue());
                customerRel.setEntityId(customerId);
                customerAccountService.addCustomerAccountRel(customerRel);

                CustomerAccountRelDO opportunityRel = new CustomerAccountRelDO();
                opportunityRel.setCustomerAccountId(leadCustomerAccount.getAccountId());
                opportunityRel.setEntityType(CustomerAccountService.EntityType.OPPORTUNITY.getValue());
                opportunityRel.setEntityId(opportunityId);
                customerAccountService.addCustomerAccountRel(opportunityRel);
            });
        }

        //TODO 补上关联商务的社交账号信息

        // 更新线索状态，清空提醒时间和失效原因
        updateStatusChange(req, 0, null, opportunityId);
        
        // 设置跟进内容
        req.setContent(String.format("线索已创建客户和商机，客户名称：%s, 商机ID：%s", customerId, opportunityId));
    }
    
    /**
     * 处理状态变为待跟进的情况
     */
    private void handleTodoFollowStatus(LeadFollowReq req) {
        // 清空所有跟进记录
        LambdaQueryWrapper<LeadFollowDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeadFollowDO::getLeadId, req.getLeadId());
        leadFollowMapper.delete(queryWrapper);
        
        // 更新线索状态，清空提醒时间和失效原因
        updateStatusChange(req, 0, null, 0L);
    }
    
    /**
     * 处理其他状态变更的情况
     */
    private void handleFollowingStatus(LeadFollowReq req) {
        // 更新线索状态，清空提醒时间和失效原因
        updateStatusChange(req, 0, null, 0L);
    }

    private void updateStatusChange(LeadFollowReq req, Integer invalidReason, LocalDateTime remindTime, Long opportunityId) {
        LambdaUpdateWrapper<LeadDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LeadDO::getId, req.getLeadId())
                .set(LeadDO::getStatus, req.getStatus())
                .set(LeadDO::getRemindTime, remindTime)
                .set(LeadDO::getOpportunityId, opportunityId)
                .set(LeadDO::getInvalidReason, invalidReason).set(LeadDO::getLastFollowTime, req.getFollowTime());

        baseMapper.update(null, updateWrapper);
    }

    
    /**
     * 添加跟进记录
     */
    private void addFollowRecord(LeadFollowReq req) {
        LeadFollowDO followDO = new LeadFollowDO();
        followDO.setLeadId(req.getLeadId());
        followDO.setContent(req.getContent());
        followDO.setAttachment(req.getAttachment());
        followDO.setFollowTime(req.getFollowTime());
        followDO.setFollowUserId(StpUtil.getLoginIdAsLong());
        leadFollowMapper.insert(followDO);
    }
    

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFollow(LeadFollowUpdateReq req) {
        // 1. 校验跟进记录是否存在
        LeadFollowDO followDO = leadFollowMapper.selectById(req.getId());
        ValidationUtils.throwIf(followDO == null, "跟进记录不存在");
        
        // 2. 校验是否有权限修改（只能修改自己创建的记录）
        ValidationUtils.throwIf(!followDO.getFollowUserId().equals(StpUtil.getLoginIdAsLong()), "只能修改自己创建的跟进记录");
        
        // 3. 更新跟进记录
        LambdaUpdateWrapper<LeadFollowDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LeadFollowDO::getId, req.getId())
                .set(LeadFollowDO::getContent, req.getContent())
                .set(LeadFollowDO::getAttachment, req.getAttachment())
                .set(LeadFollowDO::getFollowTime, req.getFollowTime());
        
        leadFollowMapper.update(null, updateWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFollow(Long id) {
        // 1. 校验跟进记录是否存在
        LeadFollowDO followDO = leadFollowMapper.selectById(id);
        ValidationUtils.throwIf(followDO == null, "跟进记录不存在");
        
        // 2. 校验是否有权限删除（只能删除自己创建的记录）
        ValidationUtils.throwIf(!followDO.getFollowUserId().equals(StpUtil.getLoginIdAsLong()), "只能删除自己创建的跟进记录");
        
        // 3. 删除跟进记录
        leadFollowMapper.deleteById(id);
    }
    
    @Override
    public PageResp<LeadFollowResp> pageFollow(LeadFollowQuery query) {
        // 1. 校验线索是否存在
        LeadDO leadDO = baseMapper.selectById(query.getLeadId());
        ValidationUtils.throwIf(leadDO == null, "线索不存在");
        
        // 2. 分页查询跟进记录
        Page<LeadFollowDO> page = new Page<>(query.getPage(), query.getSize());
        LambdaQueryWrapper<LeadFollowDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeadFollowDO::getLeadId, query.getLeadId())
                .orderByDesc(LeadFollowDO::getFollowTime);
        
        Page<LeadFollowDO> resultPage = leadFollowMapper.selectPage(page, queryWrapper);
        PageResp<LeadFollowResp> resultData = PageResp.build(resultPage, LeadFollowResp.class);
        // 3. 转换为响应对象
        resultData.getList().forEach(resp -> {
            resp.setFollowUserName(userService.getNickName(resp.getFollowUserId()));
        });


        return resultData;
    }
    
    @Override
    public void batchUpdateHandler(LeadUpdateHandlerReq req) {
        LambdaUpdateWrapper<LeadDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(LeadDO::getId, req.getIds())
                .set(LeadDO::getHandlerUserId, req.getHandlerUserId());
        baseMapper.update(null, updateWrapper);
    }


    @Override
    protected void beforeAdd(LeadReq req) {
        if(null == req.getHandlerUserId()) {
            req.setHandlerUserId(StpUtil.getLoginIdAsLong());
        }

        //判断客户是否存在
        CustomerDO customer = customerService.getByName(req.getCustomerName());
        ValidationUtils.throwIf(null != customer, "重复客户名称，客户已存在");

    }

    @Override
    protected void afterAdd(LeadReq req, LeadDO entity) {
        // 添加客户账号信息
        if(CollUtil.isNotEmpty(req.getAccounts())) {
            customerAccountService.saveCustomerAccount(req.getAccounts(), CustomerAccountService.EntityType.LEAD.getValue(), entity.getId());
        }

        LeadFollowReq followReq = new LeadFollowReq();
        followReq.setLeadId(entity.getId());
        followReq.setContent("创建线索");
        followReq.setFollowTime(LocalDateTime.now());
        addFollowRecord(followReq);

        LeadDO updateEntity = new LeadDO();
        updateEntity.setId(entity.getId());
        updateEntity.setLastFollowTime(LocalDateTime.now());
        baseMapper.updateById(updateEntity);
    }

    @Override
    protected void afterUpdate(LeadReq req, LeadDO entity) {
        customerAccountService.saveCustomerAccount(req.getAccounts(), CustomerAccountService.EntityType.LEAD.getValue(), entity.getId());
    }

    @Override
    protected void afterDelete(List<Long> ids) {
        // 删除关联的客户账号
        for (Long id : ids) {
            customerAccountService.deleteCustomerAccount(CustomerAccountService.EntityType.LEAD.getValue(), id);
        }
    }

    @Override
    protected void beforeDelete(List<Long> ids) {
        ids.forEach(id -> {
            LeadDO old = baseMapper.selectById(id);
            ValidationUtils.throwIf(old == null, "线索不存在，不能删除");
            ValidationUtils.throwIf(LeadStatusEnum.CREATE_OPPORTUNITY.equals(old.getStatus()), "已创建商机的线索不能删除");
        });
    }



    @Override
    protected void beforeUpdate(LeadReq req, Long id) {
        LeadDO old = baseMapper.selectById(id);
        ValidationUtils.throwIf(old == null, "线索不存在，不能修改");


        //判断客户是否存在
        CustomerDO customer = customerService.getByName(req.getCustomerName());
        ValidationUtils.throwIf(null != customer, "重复客户名称，客户已存在");

//        // 如果状态变为已关闭，则由系统自动添加一条跟进记录
//        if (req.getStatus() != null && LeadStatusEnum.INVALID.equals(req.getStatus())
//                && !LeadStatusEnum.INVALID.equals(old.getStatus())) {
//            LeadFollowDO followDO = new LeadFollowDO();
//            followDO.setLeadId(id);
//            followDO.setContent("系统自动记录：线索状态变更为无效");
//            followDO.setFollowTime(LocalDateTime.now());
//            followDO.setFollowUserId(StpUtil.getLoginIdAsLong());
//            leadFollowMapper.insert(followDO);
//        }
    }
}