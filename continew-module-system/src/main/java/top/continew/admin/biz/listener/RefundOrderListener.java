/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.RefundOrderCardStatusEnum;
import top.continew.admin.biz.enums.RefundOrderStatusEnum;
import top.continew.admin.biz.event.*;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RefundOrderDO;
import top.continew.admin.biz.model.req.RefundOrderFinishReq;
import top.continew.admin.biz.model.resp.AdAccountCardOpsResultResp;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.CustomerBalanceRecordService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.RefundOrderService;
import top.continew.admin.system.service.FileService;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
@Slf4j
@RequiredArgsConstructor
public class RefundOrderListener {

    private final CustomerService customerService;

    private final AdAccountService adAccountService;

    private final RefundOrderService refundOrderService;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final FileService fileService;

    @EventListener
    public void handle(RefundOrderHandleEvent event) {
        //        Long orderId = (Long)event.getSource();
        //        RefundOrderDO order = refundOrderService.getById(orderId);
        //        String currentUser = UserContextHolder.getUsername();
        //        log.info("【退款订单】{}已被{}接单", orderId, currentUser);
        //        String message = order.getPlatformAdId() + " 正在减款中";
        //        CustomerDO customer = customerService.getById(order.getCustomerId());
        //        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //            .chatId(customer.getTelegramChatId())
        //            .replyToMessageId(order.getApplyMessageId())
        //            .text(message)
        //            .build()));
    }

    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void finish(RefundOrderFinishEvent event) {
        RefundOrderFinishReq req = (RefundOrderFinishReq)event.getSource();
        Long orderId = req.getId();
        refundOrderService.update(Wrappers.<RefundOrderDO>lambdaUpdate()
            .set(StringUtils.isNotBlank(req.getCertificate()), RefundOrderDO::getCertificate, req.getCertificate())
            .set(StringUtils.isNotBlank(req.getRemark()), RefundOrderDO::getRemark, req.getRemark())
            .set(RefundOrderDO::getFinishTime, LocalDateTime.now())
            .set(RefundOrderDO::getStatus, RefundOrderStatusEnum.FINISH)
            .eq(RefundOrderDO::getId, req.getId()));
        RefundOrderDO order = refundOrderService.getById(orderId);
        CustomerDO customer = customerService.getById(order.getCustomerId());
        log.info("【退款订单】{}已完成", orderId);
        customerService.changeAmount(order.getCustomerId(), order.getPlatformAdId(), order.getAmount(), CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE, null, null);
        log.info("【退款订单】{}客户余额添加成功", orderId);
        String replyMsg = order.getPlatformAdId() + " 已减款 " + order.getAmount();
        InputFile inputFile = fileService.getFileInputFile(order.getCertificate());
        if (inputFile != null) {
            SendPhoto sendPhoto = SendPhoto.builder()
                .chatId(customer.getTelegramChatId())
                .photo(inputFile)
                .caption(replyMsg)
                .build();
            SpringUtil.publishEvent(new TelegramMessageEvent(sendPhoto));
        } else {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(customer.getTelegramChatId())
                .text(replyMsg)
                .build()));
        }
        // 发送额度汇总信息
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalAmountByType(customer.getId(), CustomerBalanceTypeEnum.SYSTEM);
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), order.getAmount());
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
    }

    @EventListener
    public void cancel(RefundOrderCancelEvent event) {
        Long orderId = (Long)event.getSource();
        RefundOrderDO order = refundOrderService.getById(orderId);
        log.info("【减款订单】{}已被取消", orderId);
        refundOrderService.update(Wrappers.<RefundOrderDO>lambdaUpdate()
            .set(RefundOrderDO::getStatus, RefundOrderStatusEnum.CANCEL)
            .eq(RefundOrderDO::getId, orderId));
        //        String message = order.getPlatformAdId() + " 取消减款";
        //        CustomerDO customer = customerService.getById(order.getCustomerId());
        //        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //            .chatId(customer.getTelegramChatId())
        //            .replyToMessageId(order.getApplyMessageId())
        //            .text(message)
        //            .build()));
        if (order.getCardStatus().equals(RefundOrderCardStatusEnum.SUCCESS)) {
//            AdAccountCardOpsResultResp resultResp = adAccountService.rechargeMasterCard(order.getPlatformAdId(), order.getAmount(), false);
//            if (resultResp.getIsSuccess()) {
//                refundOrderService.update(Wrappers.<RefundOrderDO>lambdaUpdate()
//                    .set(RefundOrderDO::getCardStatus, RefundOrderCardStatusEnum.REVOKE)
//                    .eq(RefundOrderDO::getId, orderId));
//            } else {
//                refundOrderService.update(Wrappers.<RefundOrderDO>lambdaUpdate()
//                    .set(RefundOrderDO::getRemark, "卡台自动撤销失败，请手动处理")
//                    .eq(RefundOrderDO::getId, orderId));
//            }
        }
    }

    /**
     * 提现卡台金额
     *
     * @param event
     */
    @Async
    @EventListener
    public void create(RefundOrderCreateEvent event) {
//        Long orderId = (Long)event.getSource();
//        RefundOrderDO order = refundOrderService.getById(orderId);
//        AdAccountCardOpsResultResp resultResp = adAccountService.withdrawMasterCard(order.getPlatformAdId(), order.getAmount());
//        refundOrderService.update(Wrappers.<RefundOrderDO>lambdaUpdate()
//            .set(RefundOrderDO::getCardStatus, resultResp.getIsSuccess()
//                ? RefundOrderCardStatusEnum.SUCCESS
//                : RefundOrderCardStatusEnum.FAIL)
//            .set(resultResp.getMessage() != null, RefundOrderDO::getRemark, resultResp.getMessage())
//            .eq(RefundOrderDO::getId, orderId));
    }
}
