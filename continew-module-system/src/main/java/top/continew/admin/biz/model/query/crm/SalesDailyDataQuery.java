package top.continew.admin.biz.model.query.crm;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 商务每日数据查询条件
 *
 * <AUTHOR>
 * @since 2025/07/11 14:30
 */
@Data
@Schema(description = "商务每日数据查询条件")
public class SalesDailyDataQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线索ID
     */
    @Schema(description = "线索ID")
    @Query(type = QueryType.EQ)
    private Long leadId;

    /**
     * 记录添加日期，默认为当前日期
     */
    @Schema(description = "记录添加日期，默认为当前日期")
    @Query(type = QueryType.BETWEEN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDate[] recordDate;

    private Long createUser;

    /**
     * 添加方式，例如：被动，主动
     */
    @Schema(description = "添加方式，例如：被动，主动")
    @Query(type = QueryType.EQ)
    private Integer addMethod;

    /**
     * 客户账号类型，例如：微信，tg
     */
    @Schema(description = "客户账号类型，例如：微信，tg")
    @Query(type = QueryType.EQ)
    private Integer accountType;

    /**
     * 客户的微信号或tg id
     */
    @Schema(description = "客户的微信号或tg id")
    @Query(type = QueryType.EQ)
    private String customerAccount;

    private String customerName;
}