package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "BM封禁分析查询条件")
public class BusinessManagerBanStatQuery implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "统计日期")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] statisticsDate;

    @Schema(description = "渠道ID")
    @Query(type = QueryType.EQ)
    private Long channelId;


    /**
     * 是否按照渠道分组
     */
    private Boolean groupByChannel = false;

    private @Min(
            value = 1L,
            message = "页码最小值为 {value}"
    ) Integer page = 1;
    @Schema(
            description = "每页条数",
            example = "10"
    )
    private @Range(
            min = 1L,
            max = 1000L,
            message = "每页条数（取值范围 {min}-{max}）"
    ) Integer size = 10;
}