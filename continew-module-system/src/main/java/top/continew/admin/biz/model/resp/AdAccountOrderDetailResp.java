/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 下户订单详情信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "下户订单详情信息")
public class AdAccountOrderDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 支付时间
     */
    @Schema(description = "下户时间")
    @ExcelProperty(value = "下户时间")
    private LocalDateTime payTime;

    @ExcelProperty(value = "授权时间")
    private LocalDateTime finishTime;

    @ExcelProperty(value = "回收时间")
    private LocalDateTime recycleTime;

    private Long customerId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @ExcelProperty(value = "关联客户")
    private String customerName;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @ExcelProperty(value = "时区")
    private String timezone;

    /**
     * 客户BM ID
     */
    @Schema(description = "客户BM ID")
    @ExcelProperty(value = "客户BM ID")
    private String customerBmId;

    @Schema(description = "广告户名称")
    @ExcelProperty(value = "广告户名称")
    private String adAccountName;

    @ExcelProperty(value = "关联BM")
    private String adAccountBmId;

    @ExcelProperty(value = "BM类型", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerTypeEnum bmType;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @ExcelProperty(value = "关联广告户")
    private String adAccountId;

    /**
     * 关联广告户
     */
    @Schema(description = "广告户状态")
    @ExcelProperty(value = "广告户状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountStatusEnum accountStatus;

    @ExcelProperty(value = "坑位费")
    private BigDecimal accountCost;

    @ExcelProperty(value = "停用时间")
    private LocalDateTime banTime;

    @ExcelProperty(value = "总充值")
    private BigDecimal rechargeAmount;

    @ExcelProperty(value = "fb余额")
    private BigDecimal fbBalance;

    @ExcelProperty(value = "总消耗")
    private BigDecimal totalSpent;

    @ExcelProperty(value = "当前消耗")
    private BigDecimal amountSpent;


    @ExcelProperty(value = "卡台消耗")
    private BigDecimal cardSpent;

    /**
     * 状态
     */
    @Schema(description = "下户状态")
    @ExcelProperty(value = "下户状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountOrderStatusEnum status;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    @ExcelProperty(value = "开户费")
    private BigDecimal payAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "标签")
    private String tags;

    @ExcelProperty(value = "下户成本")
    private BigDecimal cost;

    @ExcelProperty(value = "处理人")
    private String handleUserName;

    /**
     * 上系列时间
     */
    @ExcelProperty(value = "上系列时间")
    private LocalDateTime startCampaignTime;

    @ExcelProperty(value = "是否预充")
    private Boolean enablePrepay;

    private BigDecimal prepayAccountBalance;

    @ExcelProperty(value = "是否一刀流")
    private Boolean isOneDollar;

    @ExcelProperty(value = "认领BM时间")
    private LocalDateTime bmAuthTime;

    private Integer costParty;

}