/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.AdAccountOrderStatisticsQuery;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 下户订单 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
public interface AdAccountOrderMapper extends BaseMapper<AdAccountOrderDO> {

    IPage<AdAccountOrderResp> selectCustomPage(@Param("page") IPage<AdAccountOrderDO> page,
                                               @Param(Constants.WRAPPER) QueryWrapper<AdAccountOrderDO> queryWrapper);

    List<AdAccountOrderResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<AdAccountOrderDO> queryWrapper);

    IPage<AdAccountSalesStatisticsResp> selectAdAccountSalesStatistics(@Param("page") IPage<?> page,
                                                                       @Param("start") LocalDate start,
                                                                       @Param("end") LocalDate end);

    List<AdAccountSalesStatisticsResp> listAdAccountSalesStatistics(@Param("start") LocalDate start,
                                                                    @Param("end") LocalDate end);

    AdAccountSalesStatisticsResp getAdAccountSalesStatisticsSummary(@Param("start") LocalDate start,
                                                                    @Param("end") LocalDate end);

    /**
     * 查询仪表盘 下户 总览
     *
     * @return 仪表盘 下户 总览
     */
    DashboardOverviewCommonResp selectDashboardOverviewSale();

    @Cached(key = "#months[0]", name = CacheConstants.DASHBOARD_KEY_PREFIX + "SALE:")
    List<DashboardChartCommonResp> selectListDashboardAnalysisAdAccountSale(@Param("months") List<String> months);

    Integer calAccountOrderTotalSpent(@Param("status") Integer status);

    /**
     * 查询时区出售统计
     *
     * @param statTimes
     * @return
     */
    List<DashboardChartCommonResp> selectTimezoneSaleStat(@Param("statTimes") LocalDateTime[] statTimes);

    /**
     * 获取有效的订单列表
     *
     * @param customerId
     * @return
     */
    List<AdAccountOrderResp> selectCustomerUnClearOrderList(@Param("customerId") Long customerId);

    IPage<UnSpentOrderResp> selectNoSpentOrderPage(@Param("page") IPage<AdAccountOrderDO> page,
                                                   @Param("day") Integer day,
                                                   @Param(Constants.WRAPPER) QueryWrapper<AdAccountOrderDO> queryWrapper);

    List<UnSpentOrderResp> selectNoSpentOrderList(@Param("day") Integer day,
                                                  @Param(Constants.WRAPPER) QueryWrapper<AdAccountOrderDO> queryWrapper);

    IPage<AdAccountOrderResp> selectInsufficientBalanceOrderPage(@Param("page") IPage<AdAccountOrderDO> page,
                                                                 @Param(Constants.WRAPPER) QueryWrapper<AdAccountOrderDO> queryWrapper);

    List<UserOrderStatResp> selectUserOrderStat(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    List<AdAccountStatisticsResp> selectStatistics(@Param("statTimes") LocalDateTime[] statTimes);

    /**
     * 查询下户订单统计
     *
     * @param query
     * @return
     */
    List<AdAccountOrderStatisticsResp> sumOrderStatistics(@Param("query") AdAccountOrderStatisticsQuery query);

    /**
     * 获取客户名称
     *
     * @param platformAdId
     * @return
     */
    @Select("select c.name from biz_ad_account_order o left join biz_customer c on c.id = o.customer_id where o.ad_account_id = #{platformAdId} and o.status = 3")
    String getCustomerName(@Param("platformAdId") String platformAdId);

    BigDecimal getTotalFbBalance(@Param("settleType") Integer settleType);

    /**
     * 获取客户fb余额
     *
     * @param customerId
     * @return
     */
    BigDecimal getCustomerFbBalance(@Param("customerId") Long customerId);

    List<AdAccountOrderDO> selectCompleteAndNormalAdAccountOrderList();

    /**
     * 获取客户每日账户数据
     *
     * @param statDate
     * @return
     */
    List<CustomerDailyStatResp> getCustomerDailyStat(@Param("statDate") LocalDate statDate);
}