/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.ClearOrderStatusEnum;
import top.continew.admin.biz.event.ClearOrderCreateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.ClearOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotClearReq;
import top.continew.admin.biz.robot.strategy.BaseRobotCommandService;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.ClearOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.starter.core.validation.CheckUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobotClearStrategyImpl extends BaseRobotCommandService implements RobotCommandStrategy {

    private final ClearOrderService clearOrderService;

    private final AdAccountService adAccountService;

    private final AdAccountOrderService adAccountOrderService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.CLEAR;
    }

    @Override
    public String execute(Update update) {
        if (!this.hasPermission(this.getCommand(), update.getMessage().getChatId())) {
            return null;
        }
        RobotClearReq req = BotUtils.parseBotMsgToObject(update.getMessage().getText(), "\n", ":", RobotClearReq.class);
        log.info("{}收到一条清零请求：{}", getCommand().getDescription(), JSONObject.toJSONString(req));
        CustomerDO customer = getCustomer(req.getCustomerName(), update.getMessage().getChatId());
        AdAccountDO adAccount = adAccountService.getOne(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNull(adAccount, "未找到相关广告户");
        adAccountOrderService.checkExistOrder(customer.getId(), req.getPlatformAdId());
        CheckUtils.throwIf(!adAccount.getClearStatus().equals(AdAccountClearStatusEnum.WAIT), "请勿重复清零");
        ClearOrderDO order = new ClearOrderDO();
        order.setOrderNo(CommonUtils.randomOrderNo("QL"));
        order.setCustomerId(customer.getId());
        order.setPlatformAdId(req.getPlatformAdId());
        order.setStatus(ClearOrderStatusEnum.WAIT);
        order.setApplyMessageId(update.getMessage().getMessageId());
        clearOrderService.save(order);
        // 更新广告户清零状态
        adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getClearStatus, AdAccountClearStatusEnum.PROCESS)
            .eq(AdAccountDO::getId, adAccount.getId()));
        log.info("{}订单入库成功", getCommand().getDescription());
        ClearOrderCreateEvent clearOrderCreateEvent = new ClearOrderCreateEvent(order.getId());
        SpringUtil.publishEvent(clearOrderCreateEvent);
        return "订单%s提交成功，等待客服人员处理".formatted(order.getOrderNo());
    }
}
