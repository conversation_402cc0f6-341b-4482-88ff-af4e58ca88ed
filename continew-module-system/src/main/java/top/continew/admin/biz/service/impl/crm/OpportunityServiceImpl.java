package top.continew.admin.biz.service.impl.crm;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.LeadStatusEnum;
import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.mapper.crm.OpportunityMapper;
import top.continew.admin.biz.mapper.crm.OpportunityFollowMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.crm.LeadFollowDO;
import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.entity.crm.OpportunityFollowDO;
import top.continew.admin.biz.model.query.crm.OpportunityFollowQuery;
import top.continew.admin.biz.model.query.crm.OpportunityQuery;
import top.continew.admin.biz.model.req.crm.*;
import top.continew.admin.biz.model.resp.crm.*;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.crm.CustomerAccountService;
import top.continew.admin.biz.service.crm.OpportunityService;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.ValidationUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商机业务实现
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Service
@RequiredArgsConstructor
public class OpportunityServiceImpl extends BaseServiceImpl<OpportunityMapper, OpportunityDO, OpportunityResp, OpportunityDetailResp, OpportunityQuery, OpportunityReq> implements OpportunityService {
    private final OpportunityFollowMapper opportunityFollowMapper;
    private final UserService userService;
    private final CustomerAccountService customerAccountService;
    private final CustomerService customerService;

    @Override
    public PageResp<OpportunityResp> page(OpportunityQuery query, PageQuery pageQuery) {
        Page<OpportunityResp> result = baseMapper.selectOpportunityPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);
        PageResp<OpportunityResp> pageResp = PageResp.build(result, OpportunityResp.class);

        pageResp.getList().forEach(resp -> {
            if (!OpportunityStatusEnum.TO_FOLLOW_UP.equals(resp.getStatus())) {
                OpportunityFollowDO lastFollow = opportunityFollowMapper.selectOne(new LambdaQueryWrapper<OpportunityFollowDO>()
                        .eq(OpportunityFollowDO::getOpportunityId, resp.getId())
                        .orderByDesc(OpportunityFollowDO::getFollowTime)
                        .last("limit 1"));
                OpportunityFollowResp lastFollowResp = new OpportunityFollowResp();
                BeanUtil.copyProperties(lastFollow, lastFollowResp);
                resp.setLastFollow(lastFollowResp);
            }

            resp.setCustomer(customerService.get(resp.getCustomerId()));

            this.fill(resp);
        });

        return pageResp;
    }

    @Override
    public OpportunityDetailResp get(Long id) {
        OpportunityDetailResp detail = super.get(id);
        if (detail != null) {
            detail.setAccounts(customerAccountService.listCustomerAccounts(CustomerAccountService.EntityType.OPPORTUNITY.getValue(), id));
            detail.setCustomer(customerService.get(detail.getCustomerId()));
            if(null != detail.getCustomer()) {
                detail.setCompanyName(detail.getCustomer().getCompanyName());
                detail.setCity(detail.getCustomer().getCity());
                detail.setCustomerPosition(detail.getCustomer().getCustomerPosition());
                detail.setTeamSize(detail.getCustomer().getTeamSize());
                detail.setDailyTeamSpending(detail.getCustomer().getDailyTeamSpending());
                detail.setProductName(detail.getCustomer().getProductName());
            }

        }

        return detail;
    }

    @Override
    protected void beforeAdd(OpportunityReq req) {
        if (null == req.getHandlerUserId()) {
            req.setHandlerUserId(StpUtil.getLoginIdAsLong());
        }
    }

    @Override
    protected void afterAdd(OpportunityReq req, OpportunityDO entity) {
        // 添加客户账号信息
        if (CollUtil.isNotEmpty(req.getAccounts())) {
            customerAccountService.saveCustomerAccount(req.getAccounts(), CustomerAccountService.EntityType.OPPORTUNITY.getValue(), entity.getId());
        }
    }

    @Override
    protected void afterUpdate(OpportunityReq req, OpportunityDO entity) {
        customerAccountService.saveCustomerAccount(req.getAccounts(), CustomerAccountService.EntityType.OPPORTUNITY.getValue(), entity.getId());

        boolean flag = StringUtils.isNotBlank(req.getCompanyName()) ||
                StringUtils.isNotBlank(req.getCustomerPosition()) ||
                StringUtils.isNotBlank(req.getCity()) ||
                StringUtils.isNotBlank(req.getProductName()) ||
                req.getTeamSize() != null ||
                req.getDailyTeamSpending() != null;

        if (flag) {
            customerService.lambdaUpdate()
                    .eq(CustomerDO::getId, entity.getCustomerId())
                    .set(StringUtils.isNotBlank(req.getCompanyName()), CustomerDO::getCompanyName, req.getCompanyName())
                    .set(StringUtils.isNotBlank(req.getCustomerPosition()), CustomerDO::getCustomerPosition, req.getCustomerPosition())
                    .set(StringUtils.isNotBlank(req.getCity()), CustomerDO::getCity, req.getCity())
                    .set(StringUtils.isNotBlank(req.getProductName()), CustomerDO::getProductName, req.getProductName())
                    .set(req.getTeamSize() != null, CustomerDO::getTeamSize, req.getTeamSize())
                    .set(req.getDailyTeamSpending() != null, CustomerDO::getDailyTeamSpending, req.getDailyTeamSpending())
                    .update();
        }
    }

    @Override
    protected void afterDelete(List<Long> ids) {
        // 删除关联的客户账号
        for (Long id : ids) {
            customerAccountService.deleteCustomerAccount(CustomerAccountService.EntityType.OPPORTUNITY.getValue(), id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFollow(OpportunityFollowReq req) {
        /*
        1、前置条件：状态、跟进时间、商机ID不能为空，已经在外层校验过了
        2、判断状态是否有发生变化
        （1）状态没有变化
            1）即在当前状态下，添加一条跟进记录，则跟进内容不能为空
        （2）状态发生变化
            1）状态变为流失，则需要有流失原因和跟进内容，变更商机的状态和流失原因以及添加一条跟进记录
            2）状态变为长期跟进，则需要有提醒时间和跟进内容，变更商机的状态和提醒时间以及添加一条跟进记录
            3）状态变为赢单，修改客户类型为正式客户，添加一条跟进记录，跟进内容为"商机已赢单，客户转为正式客户"
            4）状态不能从赢单状态，再回退到其他状态。
            5）状态非长期跟进，则清空提醒时间
            6）状态非无效，则清空失效原因
            7）状态变为待跟进，则清空所有跟进记录
         */

        req.setContent(null == req.getContent() ? StrUtil.EMPTY : req.getContent());

        // 1. 校验商机是否存在
        OpportunityDO opportunityDO = baseMapper.selectById(req.getOpportunityId());
        ValidationUtils.throwIf(opportunityDO == null, "商机不存在");

        // 2. 判断状态是否有发生变化
        boolean statusChanged = req.getStatus() != null && !req.getStatus().equals(opportunityDO.getStatus());

        // 3. 根据状态变化情况处理
        if (!statusChanged) {
            // 状态没有变化，添加跟进记录
            handleNoStatusChange(req);
        } else {
            // 状态发生变化
            handleStatusChange(req, opportunityDO);
        }

        // 4. 添加跟进记录
        addFollowRecord(req);

    }

    /**
     * 处理状态没有变化的情况
     */
    private void handleNoStatusChange(OpportunityFollowReq req) {

        LambdaUpdateWrapper<OpportunityDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OpportunityDO::getId, req.getOpportunityId())
                .set(OpportunityDO::getLastFollowTime, req.getFollowTime());
        baseMapper.update(null, updateWrapper);
    }

    /**
     * 处理状态发生变化的情况
     */
    private void handleStatusChange(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        // 不能从赢单状态回退到其他状态
        ValidationUtils.throwIf(
                opportunityDO.getStatus() == OpportunityStatusEnum.WON && req.getStatus() != OpportunityStatusEnum.WON,
                "已赢单的商机不能回退到其他状态"
        );

        switch (req.getStatus()) {
            case WON:
                handleWinStatus(req, opportunityDO);
                break;
            case LOST:
                handleLostStatus(req, opportunityDO);
                break;
            case TO_FOLLOW_UP:
                handleTodoFollowStatus(req, opportunityDO);
                break;
            case FOLLOWING_UP:
                handleFollowingStatusChange(req, opportunityDO);
                break;
            case LONG_TERM_FOLLOW_UP:
                handleLongTermFollowStatus(req, opportunityDO);
                break;
            default:
                throw new BusinessException("状态非法");
        }

    }

    /**
     * 处理状态变为流失的情况
     */
    private void handleLostStatus(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        // 流失状态必须提供流失原因
        ValidationUtils.throwIf(req.getInvalidReason() == null, "流失状态必须提供流失原因");
        // 更新商机状态和流失原因
        updateStatus(req, req.getInvalidReason(), null, req.getFollowTime());
    }

    private void updateStatus(OpportunityFollowReq req, Integer invalidReason, LocalDateTime remindTime, LocalDateTime followTime) {
        LambdaUpdateWrapper<OpportunityDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OpportunityDO::getId, req.getOpportunityId())
                .set(OpportunityDO::getStatus, req.getStatus())
                .set(OpportunityDO::getLostReason, invalidReason)
                .set(OpportunityDO::getRemindTime, remindTime)
                .set(OpportunityDO::getLastFollowTime, followTime);

        baseMapper.update(null, updateWrapper);
    }

    /**
     * 处理状态变为长期跟进的情况
     */
    private void handleLongTermFollowStatus(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        // 长期跟进状态必须提供提醒时间
        ValidationUtils.throwIf(req.getRemindTime() == null, "长期跟进状态必须提供提醒时间");

        updateStatus(req, 0, req.getRemindTime(), req.getFollowTime());
    }

    /**
     * 处理状态变为赢单的情况
     */
    private void handleWinStatus(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        // 更新商机状态为赢单，并将客户类型更新为正式客户
        updateStatus(req, 0, null, req.getFollowTime());

        CustomerDO updateCustomer = new CustomerDO();
        updateCustomer.setId(opportunityDO.getCustomerId());
        updateCustomer.setType(CustomerTypeEnum.FORMAL);
        customerService.updateById(updateCustomer);

        req.setContent("商机已赢单，客户转为正式客户");
    }

    /**
     * 处理状态变为待跟进的情况
     */
    private void handleTodoFollowStatus(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        // 状态变为待跟进，清空所有跟进记录相关的状态
        updateStatus(req, 0, null, null);

        // 清空所有跟进记录
        LambdaQueryWrapper<OpportunityFollowDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpportunityFollowDO::getOpportunityId, req.getOpportunityId());
        opportunityFollowMapper.delete(queryWrapper);
    }

    /**
     * 处理跟进中状态变更的情况
     */
    private void handleFollowingStatusChange(OpportunityFollowReq req, OpportunityDO opportunityDO) {
        updateStatus(req, 0, null, req.getFollowTime());
    }

    /**
     * 添加跟进记录
     */
    private void addFollowRecord(OpportunityFollowReq req) {
        OpportunityFollowDO followDO = new OpportunityFollowDO();
        followDO.setOpportunityId(req.getOpportunityId());
        followDO.setContent(req.getContent());
        followDO.setFollowTime(req.getFollowTime());
        followDO.setAttachment(req.getAttachment());
        followDO.setFollowUserId(StpUtil.getLoginIdAsLong());
        opportunityFollowMapper.insert(followDO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFollow(OpportunityFollowUpdateReq req) {
        // 1. 校验跟进记录是否存在
        OpportunityFollowDO followDO = opportunityFollowMapper.selectById(req.getId());
        ValidationUtils.throwIf(followDO == null, "跟进记录不存在");

        // 2. 校验是否有权限修改（只能修改自己创建的记录）
        ValidationUtils.throwIf(!followDO.getFollowUserId().equals(StpUtil.getLoginIdAsLong()), "只能修改自己创建的跟进记录");

        // 3. 更新跟进记录
        LambdaUpdateWrapper<OpportunityFollowDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OpportunityFollowDO::getId, req.getId())
                .set(OpportunityFollowDO::getContent, req.getContent())
                .set(OpportunityFollowDO::getAttachment, req.getAttachment())
                .set(OpportunityFollowDO::getFollowTime, req.getFollowTime());

        opportunityFollowMapper.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFollow(Long id) {
        // 1. 校验跟进记录是否存在
        OpportunityFollowDO followDO = opportunityFollowMapper.selectById(id);
        ValidationUtils.throwIf(followDO == null, "跟进记录不存在");

        // 2. 校验是否有权限删除（只能删除自己创建的记录）
        ValidationUtils.throwIf(!followDO.getFollowUserId().equals(StpUtil.getLoginIdAsLong()), "只能删除自己创建的跟进记录");

        // 3. 删除跟进记录
        opportunityFollowMapper.deleteById(id);
    }

    @Override
    public PageResp<OpportunityFollowResp> pageFollow(OpportunityFollowQuery query) {
        // 1. 校验商机是否存在
        OpportunityDO opportunityDO = baseMapper.selectById(query.getOpportunityId());
        ValidationUtils.throwIf(opportunityDO == null, "商机不存在");

        // 2. 分页查询跟进记录
        Page<OpportunityFollowDO> page = new Page<>(query.getPage(), query.getSize());
        LambdaQueryWrapper<OpportunityFollowDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpportunityFollowDO::getOpportunityId, query.getOpportunityId())
                .orderByDesc(OpportunityFollowDO::getFollowTime);

        Page<OpportunityFollowDO> resultPage = opportunityFollowMapper.selectPage(page, queryWrapper);
        PageResp<OpportunityFollowResp> resultData = PageResp.build(resultPage, OpportunityFollowResp.class);

        // 3. 转换为响应对象
        resultData.getList().forEach(resp -> {
            resp.setFollowUserName(userService.getNickName(resp.getFollowUserId()));
        });

        return resultData;
    }

    @Override
    public void batchUpdateHandler(OpportunityUpdateHandlerReq req) {
        LambdaUpdateWrapper<OpportunityDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(OpportunityDO::getId, req.getIds())
                .set(OpportunityDO::getHandlerUserId, req.getHandlerUserId());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void updateLongTermFollowUp() {
        // 将当前日期+3天
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime remindTime = now.plusDays(3);
        baseMapper.updateLongTermFollowUp(remindTime);
    }
}