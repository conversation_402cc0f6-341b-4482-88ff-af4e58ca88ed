package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotEmpty;
import top.continew.admin.biz.model.entity.CustomerOrderGroupDO;
import top.continew.admin.biz.model.req.DistributeGroupReq;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerOrderGroupQuery;
import top.continew.admin.biz.model.req.CustomerOrderGroupReq;
import top.continew.admin.biz.model.resp.CustomerOrderGroupDetailResp;
import top.continew.admin.biz.model.resp.CustomerOrderGroupResp;

import java.util.List;

/**
 * 客户下户订单分组业务接口
 *
 * <AUTHOR>
 * @since 2025/07/17 17:14
 */
public interface CustomerOrderGroupService extends BaseService<CustomerOrderGroupResp, CustomerOrderGroupDetailResp, CustomerOrderGroupQuery, CustomerOrderGroupReq>, IService<CustomerOrderGroupDO> {

    void createGroup(CustomerOrderGroupReq req);

    void deleteGroup( List<Long> ids);

    void editGroup(CustomerOrderGroupReq req);

    void distributeGroup(DistributeGroupReq req);
}