/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.model.query.*;
import top.continew.admin.biz.model.req.AdAccountOrderBatchAddByAdAccountIdReq;
import top.continew.admin.biz.model.req.AdAccountOrderBatchAddByBmTypeReq;
import top.continew.admin.biz.model.req.AdAccountOrderReq;
import top.continew.admin.biz.model.resp.AdAccountOrderStatisticsResp;
import top.continew.admin.biz.model.req.AdAccountOrderUpdateBmIdReq;
import top.continew.admin.biz.model.resp.AdAccountOrderDetailResp;
import top.continew.admin.biz.model.resp.AdAccountOrderResp;
import top.continew.admin.biz.model.resp.AdAccountStatisticsResp;
import top.continew.admin.biz.model.resp.UnSpentOrderResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.file.excel.util.ExcelUtils;
import top.continew.starter.log.annotation.Log;

import java.util.List;

/**
 * 下户订单管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Tag(name = "下户订单管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adAccountOrder", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
@RequiredArgsConstructor
public class AdAccountOrderController extends BaseController<AdAccountOrderService, AdAccountOrderResp, AdAccountOrderDetailResp, AdAccountOrderQuery, AdAccountOrderReq> {

    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/import")
    @SaCheckPermission("biz:adAccountOrder:add")
    public void exportExcel(@RequestParam("file") MultipartFile file, @RequestParam("customerId") Long customerId) {

        baseService.importExcel(file, customerId);
    }

    @Operation(summary = "批量下户", description = "批量下户")
    @PostMapping("/batch")
    public void batchAdd(@Validated @RequestBody AdAccountOrderBatchAddByBmTypeReq req) {
        baseService.batchAddByBmType(req);
    }

    @Operation(summary = "批量下户(广告户ID)", description = "批量下户(广告户ID)")
    @PostMapping("/batchAddByAdAccountId")
    public void batchAddByAdAccountId(@Validated @RequestBody AdAccountOrderBatchAddByAdAccountIdReq req) {
        baseService.batchAddByAdAccountId(req);
    }

    @Operation(summary = "接单", description = "接单")
    @PutMapping("/{id}/handle")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void taskOrder(@PathVariable("id") Long id) {
        baseService.handle(id);
    }

    @Operation(summary = "授权失败", description = "授权失败")
    @PutMapping("/{id}/authFail")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void authFail(@PathVariable("id") Long id) {
        baseService.authorizeFail(id);
    }

    @Operation(summary = "接收失败", description = "接收失败")
    @PutMapping("/{id}/receiveFail")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void receiveFail(@PathVariable("id") Long id) {
        baseService.receiveFail(id);
    }

    @Operation(summary = "作废", description = "作废")
    @PutMapping("/{id}/invalid")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void invalid(@PathVariable("id") Long id) {
        baseService.invalid(id);
    }

    @Operation(summary = "取消回收", description = "取消回收")
    @PutMapping("/{id}/cancelRecycle")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void cancelRecycle(@PathVariable("id") Long id) {
        baseService.cancelRecycle(id);
    }

    @Operation(summary = "授权成功", description = "授权成功")
    @PutMapping("/{id}/success")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void success(@PathVariable("id") Long id) {
        baseService.authorizeSuccess(id);
    }

    @Operation(summary = "取消", description = "取消")
    @PutMapping("/{id}/cancel")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void cancel(@PathVariable("id") Long id) {
        baseService.cancel(id);
    }

    @Operation(summary = "修改BM ID", description = "修改BM ID")
    @PutMapping("/bmId")
    @SaCheckPermission("biz:adAccountOrder:handle")
    public void changeBmId(@RequestBody AdAccountOrderUpdateBmIdReq req) {
        baseService.batchChangeBmId(req);
    }

    @Operation(summary = "回收订单", description = "回收订单")
    @PutMapping("/{id}/recycle")
    public void recycle(@PathVariable("id") Long id) {
        baseService.recycle(id);
    }

    @Log(ignore = true)
    @GetMapping("/timezone/stat")
    public List<DashboardChartCommonResp> getTimezoneStat(TimezoneSaleStatQuery query) {
        return baseService.selectTimezoneSaleStat(query);
    }

    @Operation(summary = "退款", description = "退款")
    @PutMapping("/{id}/refund")
    @SaCheckPermission("biz:adAccountOrder:refund")
    public void refund(@PathVariable("id") Long id) {
        baseService.refund(id);
    }

    @Log(ignore = true)
    @GetMapping("/unspent/page")
    public PageResp<UnSpentOrderResp> selectUnSpentOrderPage(InactiveAccountAnalyzeQuery query) {
        return baseService.selectUnSpentOrderPage(query);
    }

    @Log(ignore = true)
    @GetMapping("/unspent/export")
    public void selectUnSpentOrderPage(InactiveAccountAnalyzeQuery query, HttpServletResponse response) {
        List<UnSpentOrderResp> list = baseService.selectUnSpentOrderList(query);
        ExcelUtils.export(list, "导出数据", UnSpentOrderResp.class, response);
    }

    @Log(ignore = true)
    @GetMapping("/insufficientBalance/page")
    public PageResp<AdAccountOrderResp> selectInsufficientBalanceOrderPage(InsufficientBalanceAdAccountQuery query,
                                                                           PageQuery pageQuery) {
        return baseService.selectInsufficientBalanceOrderPage(query, pageQuery);
    }

    @Log(ignore = true)
    @GetMapping("/statistics")
    public List<AdAccountStatisticsResp> selectStatistics(AdAccountStatisticsQuery query) {
        return baseService.selectStatistics(query);
    }

    @Log(ignore = true)
    @Operation(summary = "下户订单统计", description = "下户订单统计")
    @GetMapping("/orderStatistics")
    public List<AdAccountOrderStatisticsResp> selectOrderStatistics(AdAccountOrderStatisticsQuery query) {
        return baseService.selectOrderStatistics(query);
    }
}