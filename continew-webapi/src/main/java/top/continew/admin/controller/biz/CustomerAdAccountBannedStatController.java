package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.AdAccountSpentQuery;
import top.continew.admin.biz.model.query.BannedAnalyzeFactorQuery;
import top.continew.admin.biz.model.query.BannedAnalyzeQuery;
import top.continew.admin.biz.model.query.CustomerAnalyzeQuery;
import top.continew.admin.biz.model.resp.CustomerBanStatsResp;
import top.continew.admin.biz.model.resp.CustomerSpentResp;
import top.continew.admin.biz.service.AdAccountSpentService;
import top.continew.admin.biz.service.CustomerAdAccountBannedStatService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.log.annotation.Log;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户广告户封禁分析 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "封禁分析API")
@RestController
@RequestMapping("/biz/customer/ad_banned")
@RequiredArgsConstructor
@SaIgnore
@Log(ignore = true)
public class CustomerAdAccountBannedStatController {

    private final CustomerAdAccountBannedStatService CustomerAdAccountBannedStatService;

    @GetMapping("/page")
    @Operation(summary = "分页查询列表", description = "分页查询列表")
    public BasePageResp<CustomerBanStatsResp> page(BannedAnalyzeQuery query, PageQuery pageQuery) {
        if (query.getType() == 1) {
            return CustomerAdAccountBannedStatService.pageByCustomer(query, pageQuery);
        } else if (query.getType() == 2) {
            return CustomerAdAccountBannedStatService.pageByAdAccount(query, pageQuery);
        } else {
            return null;
        }
    }


    @GetMapping("/analyze")
    @Operation(summary = "分析影响因素", description = "分析影响因素")
    public List<CustomerBanStatsResp> analyze(BannedAnalyzeFactorQuery query) {
        if (query.getType() == 1) {
            return CustomerAdAccountBannedStatService.analyzeByCustomer(query);
        } else if (query.getType() == 2) {
            return CustomerAdAccountBannedStatService.analyzeByAdAccountId(query);
        } else {
            return List.of();
        }

    }


    @GetMapping("/timeInterval")
    public LocalDateTime[] timeInterval(String adAccountIds){
        return CustomerAdAccountBannedStatService.timeInterval(adAccountIds);
    }
}