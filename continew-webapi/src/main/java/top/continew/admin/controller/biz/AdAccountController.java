/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountCardDO;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.CampaignInsightDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.query.AdAccountQuery;
import top.continew.admin.biz.model.query.CampaignInsightQuery;
import top.continew.admin.biz.model.query.InactiveAccountAnalyzeQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.*;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广告账号管理 API
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Tag(name = "广告账号管理 API")
@RestController
@CrudRequestMapping(value = "/biz/adAccount", api = {Api.PAGE, Api.DETAIL, Api.ADD, Api.UPDATE, Api.DELETE, Api.EXPORT})
@RequiredArgsConstructor
public class AdAccountController extends BaseController<AdAccountService, AdAccountResp, AdAccountDetailResp, AdAccountQuery, AdAccountReq> {

    private final AdAccountService adAccountService;

    private final AdAccountCardService adAccountCardService;

    private final CampaignInsightService campaignInsightService;

    private final AdAccountInsightService adAccountInsightService;
    private final CardTransactionService cardTransactionService;

    @Operation(summary = "核对金额", description = "核对广告户金额与卡台金额")
    @PostMapping("/checkAmount")
    @SaCheckPermission("biz:adAccount:checkAmount")
    public AdAccountCheckAmountResp checkAmount(@Validated @RequestBody AdAccountCheckAmountReq req) {
        // 1. 获取广告户信息(主要是获取时区信息)
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(req.getPlatformAdId());

        if (null == adAccount) {
            AdAccountCheckAmountResp resp = new AdAccountCheckAmountResp();
            resp.setPlatformAdId("账户不存在[" + req.getPlatformAdId() + "]");
            return resp;
        }

        if (StrUtil.isBlank(adAccount.getTimezone())) {
            AdAccountCheckAmountResp resp = new AdAccountCheckAmountResp();
            resp.setPlatformAdId(req.getPlatformAdId());
            resp.setTimezone("时区为空");
            return resp;
        }

        // 2. 查询广告户消耗金额
        BigDecimal adAmount = adAccountInsightService.sumSpendByDateRange(adAccount.getPlatformAdId(), req.getStartDate(), req.getEndDate());

        LocalDateTime startDateTime = LocalDate.parse(req.getStartDate()).atStartOfDay();
        LocalDateTime endDateTime = LocalDate.parse(req.getEndDate()).atTime(23, 59, 59);

        BigDecimal cardAmount = cardTransactionService.sumAmountByDateRange(adAccount.getPlatformAdId(), startDateTime, endDateTime);

        // 4. 计算差值并返回
        AdAccountCheckAmountResp resp = new AdAccountCheckAmountResp();
        resp.setAdAmount(adAmount);
        resp.setCardAmount(cardAmount);
        resp.setDiffAmount(adAmount.subtract(cardAmount));
        resp.setTimezone(adAccount.getTimezone());
        resp.setPlatformAdId(req.getPlatformAdId());

        return resp;
    }

    @Log(ignore = true)
    @Operation(summary = "分页查询列表", description = "分页查询列表")
    @Override
    public BasePageResp<AdAccountResp> page(AdAccountQuery query, PageQuery pageQuery) {
        return adAccountService.customPage(query, pageQuery);
    }

    @Operation(summary = "导入数据", description = "导入数据")
    @PostMapping("/excel")
    public void exportExcel(@RequestParam("file") MultipartFile file) {
        adAccountService.importAdAccount(file);
    }

    @Operation(summary = "查询广告户银行卡", description = "查询广告户银行卡")
    @GetMapping("/card")
    public List<AdAccountCardResp> cardList(String platformAdId) {
        return adAccountCardService.listCardByPlatformAdId(platformAdId);
    }

    @Operation(summary = "设置默认银行卡", description = "设置默认银行卡")
    @PostMapping("/card/setDefault")
    public void setDefault(@Validated @RequestBody AdAccountSetCardReq req) {
        adAccountCardService.setDefault(req.getPlatformAdId(), req.getCardId());
    }

    @Operation(summary = "删除银行卡", description = "删除银行卡")
    @PostMapping("/card/delete")
    public void deleteCard(@RequestBody AdAccountDeleteCardReq req) {
        adAccountCardService.removeById(req.getId());
    }

    @GetMapping("/campaign/page")
    public Page<CampaignInsightDO> campaignPage(@Validated CampaignInsightQuery query) {

        return campaignInsightService.page(new Page<>(query.getCurrent(), query.getPageSize()), new LambdaQueryWrapper<CampaignInsightDO>().eq(CampaignInsightDO::getPlatformAdId, query.getAdAccountId())
            .orderByDesc(CampaignInsightDO::getStatDate));
    }

    @Operation(summary = "新增银行卡", description = "新增银行卡")
    @PostMapping("/card/add")
    public void addCard(@Validated @RequestBody AdAccountAddCardReq req) {
        adAccountService.addCard(req);
    }

    @Operation(summary = "开卡", description = "开卡")
    @PostMapping("/card/open")
    public void openCard(@Validated @RequestBody AdAccountOpenCardReq req) {
        adAccountCardService.openCard(req);
    }

    @Operation(summary = "修改状态", description = "修改状态")
    @PutMapping("/accountStatus")
    public void updateAccountStatus(@Validated @RequestBody AdAccountUpdateAccountStatusReq req) {
        adAccountService.updateAccountStatus(req);
    }

    @Operation(summary = "修改养号状态", description = "修改养号状态")
    @PutMapping("/keepStatus")
    public void updateKeepStatus(@Validated @RequestBody AdAccountUpdateKeepStatusReq req) {
        adAccountService.updateKeepStatus(req);
    }

    @Operation(summary = "修改标签", description = "修改标签")
    @PutMapping("/tag")
    public void updateTag(@Validated @RequestBody AdAccountUpdateTagReq req) {
        adAccountService.updateTag(req);
    }

    @Operation(summary = "修改备注", description = "修改备注")
    @PutMapping("/remark")
    public void updateRemark(@Validated @RequestBody AdAccountUpdateRemarkReq req) {
        adAccountService.updateRemark(req);
    }

    @Operation(summary = "修改bm1", description = "修改bm1")
    @PutMapping("/bm1")
    public void updateBm1Browser(@Validated @RequestBody AdAccountUpdateBm1Req req) {
        adAccountService.updateBm1Id(req);
    }

    @Operation(summary = "授权BM", description = "授权BM")
    @PutMapping("/bm")
    public void updateBM(@Validated @RequestBody AdAccountUpdateBMReq req) {
        adAccountService.updateBM5(req);
    }

    @Log(ignore = true)
    @PostMapping("/copyBrowser")
    public Set<String> copyBrowser(@RequestBody AdAccountQuery query) {
        return adAccountService.getCopyBrowserSet(query);
    }

    @Log(ignore = true)
    @PostMapping("/browser/check")
    public Set<String> bowserLostCheck(@RequestBody @Validated AdAccountBrowserCheckReq req) {
        return adAccountService.browserLostCheck(req);
    }

    @Log(ignore = true)
    @GetMapping("/dailyData")
    public List<Map<String, Integer>> dailyData() {
        return adAccountService.dailyData(LocalDate.now());
    }

    @Log(ignore = true)
    @PostMapping("/inactiveAccountAnalyze")
    @SaCheckPermission("biz:inactiveAccount:analyze")
    public PageResp<AdAccountResp> pageInactiveAccountAnalyze(@RequestBody @Validated InactiveAccountAnalyzeQuery query) {
        return adAccountService.pageInactiveAccounts(query);
    }

    @Log(ignore = true)
    @GetMapping("/totalInventory")
    public Integer getTotalInventory() {
        return adAccountService.getTotalInventory(new Integer[] {AdAccountSaleStatusEnum.WAIT.getValue()});
    }

    @Log(ignore = true)
    @PostMapping("/keepStatusStat")
    public List<AdAccountKeepStatusStatResp> keepStatusStat(@Validated @RequestBody AdAccountQuery query) {
        return adAccountService.getKeepStatusStat(query);
    }

    @Log(ignore = true)
    @GetMapping("/{platformAdId}/clearAmount")
    public BigDecimal getClearAmount(@PathVariable String platformAdId) {
        return adAccountService.getClearAmount(platformAdId);
    }

    @Log(ignore = true)
    @PutMapping("/updateSpent")
    public void updateSpent(@RequestBody AdAccountSpentUpdateReq req) {
        adAccountService.updateSpent(req);
    }

    @Operation(summary = "修改出售状态", description = "修改出售状态")
    @PutMapping("/saleStatus")
    public void updateSaleStatus(@Validated @RequestBody AdAccountUpdateSaleStatusReq req) {
        adAccountService.updateSaleStatus(req);
    }

    @GetMapping("/{adAccountId}/spentList")
    public List<AdAccountSpentResp> amountSpentList(@PathVariable String adAccountId) {

        return adAccountService.amountSpentList(adAccountId);
    }

    @Log(ignore = true)
    @GetMapping("/keeping/list")
    public List<AdAccountDO> listKeepingAccount(BigDecimal minSpent) {
        return adAccountService.listWaitKeepingAccount(minSpent);
    }

    @Log(ignore = true)
    @PostMapping("/checkAdAccountNurturingStatus")
    public void checkAdAccountNurturingStatus(@RequestBody IdsReq req) {
        adAccountService.checkAdAccountNurturingStatus(req);
    }

    @Operation(summary = "修改不可用原因", description = "修改不可用原因")
    @PutMapping("/unusableReason")
    @SaCheckPermission("biz:adAccount:update")
    public void updateUnusableReason(@Validated @RequestBody AdAccountUpdateUnusableReasonReq req) {
        adAccountService.updateUnusableReason(req);
    }

    @Operation(summary = "开卡", description = "开卡")
    @PostMapping("openCard")
    public CardDO open(@Validated @RequestBody CardOpenReq req) {
        return baseService.openCard(req);
    }

    @Operation(summary = "恢复可用", description = "恢复可用")
    @PutMapping("{platformAdId}/recoverUsable")
    @SaCheckPermission("biz:adAccount:update")
    public void recoverUsable(@PathVariable String platformAdId) {
        baseService.recoverUsable(platformAdId);
    }
}