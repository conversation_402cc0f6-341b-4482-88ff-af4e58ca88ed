#!/bin/bash

# ---  测试Nginx限流脚本 ---
# 目标：向验证码接口发送15次并发请求，以触发 burst=10 的限制

TARGET_URL="https://adapi.honglingyun.net/captcha/image"
REQUEST_COUNT=15

echo "开始并发测试，将向 ${TARGET_URL} 发送 ${REQUEST_COUNT} 次请求..."
echo "预期结果：前10次返回200，后续返回503"
echo "----------------------------------------------------"

# 使用一个 for 循环来连续发起请求
# 每次请求都在后台运行(&)，以达到并发效果
for i in $(seq 1 $REQUEST_COUNT)
do
    # -s: 静默模式，不显示进度条
    # -o /dev/null: 抛弃返回的图片内容，我们只关心状态码
    # -w "%{http_code}\n": 只输出HTTP响应状态码
    # --connect-timeout 5: 设置5秒连接超时
    curl -s -o /dev/null --connect-timeout 5 -w "请求 ${i}: %{http_code}\n" "${TARGET_URL}" &
done

# 等待所有后台的 curl 任务执行完毕
wait

echo "----------------------------------------------------"
echo "测试完成。"
